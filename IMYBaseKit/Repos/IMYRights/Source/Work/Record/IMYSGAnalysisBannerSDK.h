//
//  IMYSGAnalysisBannerSDK.h
//  IMYBaseKit
//
//  Created by ljh on 2025/9/1.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class IMYSGAnalysisBannerUIConfig, IMYSGAnalysisAPIResults, RACSignal;

/// 分析会员横幅配置SDK
/// https://apidoc.seeyouyima.com/doc/68b1174faa11f24f7712332c
@interface IMYSGAnalysisBannerSDK : NSObject

+ (instancetype)sharedInstance;

/// 配置加载完成回调
@property (nonatomic, readonly) RACSignal *loadedSignal;

/// 周期天数、周期规律性、经期天数、流量变化、痛经情况、症状程度、症状数量
/// 分析结果，会使用 results 内的 item_id 做多模块匹配
- (IMYSGAnalysisBannerUIConfig *)configsForItem:(NSInteger)itemId analysisResults:(NSArray<IMYSGAnalysisAPIResults *> *)analysisResults;

/// 爱爱分析
/// 备孕各阶段时期，爱爱次数
- (IMYSGAnalysisBannerUIConfig *)configsForItem:(NSInteger)itemId pregStage:(NSInteger)preparePregStage loveCount:(NSInteger)loveCount;

/// 体温分析
/// 备孕各阶段时期，体温区间
- (IMYSGAnalysisBannerUIConfig *)configsForItem:(NSInteger)itemId pregStage:(NSInteger)preparePregStage temperature:(double)temperature;

/// 白带分析、体重
/// 备孕各阶段时期，当天最新的白带记录、最新一次记录体重的BMI范围
- (IMYSGAnalysisBannerUIConfig *)configsForItem:(NSInteger)itemId pregStage:(NSInteger)preparePregStage subItem:(NSInteger)subItem;

@end


#pragma mark - 对外暴露的配置属性

/// 分析会员横幅-UI配置
@interface IMYSGAnalysisBannerUIConfig : NSObject

/// 模块id
@property (nonatomic, assign, readonly) NSInteger item_id;

/// 素材id
@property (nonatomic, assign, readonly) NSInteger vip_material_id;

/// 显示文本，底层会轮播
@property (nonatomic, copy, readonly) NSString *text;

/// 跳转协议
@property (nonatomic, copy, readonly) NSString *uri;

/// 当前文本在素材内的索引位置
@property (nonatomic, assign, readonly) NSInteger text_index;

/// 图标（由业务自己配置）
@property (nonatomic, copy) NSString *icon;

@end

/// 业务传递的匹配结果
@interface IMYSGAnalysisAPIResults : NSObject

+ (instancetype)resultWithItem:(NSInteger)itemId timeRange:(NSInteger)timeRange resultId:(NSInteger)resultId;

/// 模块id
@property (nonatomic, assign) NSInteger item_id;

/// 周期
@property (nonatomic, assign) NSInteger time_range;

/// 分析结果
@property (nonatomic, assign) NSInteger result_id;

@end

/// 会员配置的属性
@interface IMYSGAnalysisBannerConfigRemote : NSObject

@end

NS_ASSUME_NONNULL_END
