//
//  IMYSGRecordBannerSDK.m
//  IMYBaseKit
//
//  Created by ljh on 2025/6/27.
//

#import "IMYSGRecordBannerSDK.h"
#import "IMYBaseKit.h"
#import "IMYSGAnalysisBannerSDK.h"

@interface IMYSGRecordBannerSDK ()

@property (nonatomic, assign) NSInteger retryDelay;

@property (atomic, copy) NSArray<IMYSGRecordBannerConfig *> *currentRecordConfigs;

@end

/// 分析横幅API
@interface IMYSGAnalysisBannerSDK (Update)

- (void)updateAllConfigsWithArray:(NSArray<IMYSGAnalysisBannerConfigRemote *> * const)allConfigs;

@end

@implementation IMYSGRecordBannerSDK

// 进入主页面后，触发请求banner配置
IMY_KYLIN_FUNC_MAINTAB_ASYNC {
    [IMYSGRecordBannerSDK sharedInstance];
}

+ (instancetype)sharedInstance {
    static id instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [self new];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _loadedSignal = [RACReplaySubject replaySubjectWithCapacity:1];
        [self setupABTestObservation];
    }
    return self;
}

- (void)setupABTestObservation {
    // 监听AB实验变化（AB实验又会监听一堆身份信息变化）
    [[IMYABTestManager sharedInstance].loadedSignal subscribeNext:^(id  _Nullable x) {
        // 有网络的情况，先清空当前配置
        if (IMYNetState.networkEnable) {
            [self updateAllConfigsWithArray:nil];
        }
        // 立马请求新数据
        [self requestWithImmediately:YES];
    }];
}

- (void)refresh {
    // request 内部做了请求阈值判断
    [self requestWithImmediately:NO];
}

- (void)requestWithImmediately:(const BOOL)immediately {
    NSString *queueKey = NSStringFromClass(self.class);
    NSString *retryKey = [queueKey stringByAppendingString:@"RetryX"];
    if (immediately) {
        // 立即请求
        [NSObject imy_cancelBlockForKey:retryKey];
        imy_asyncBlockExecuteBlock(^{
            [self requestReal];
        });
    } else {
        // 阈值0.1秒请求
        [NSObject imy_asyncBlock:^{
            [self requestReal];
        } onQueue:dispatch_get_global_queue(0, 0) afterSecond:0.1 forKey:retryKey];
    }
}

- (void)requestReal {
    // 目前无参数
    [[IMYPublicServerRequest getPath:@"api/v3/resource_location" host:sub_seeyouyima_com params:nil headers:nil] subscribeNext:^(IMYHTTPResponse *x) {
        // 反序列化服务端返回的数据
        NSArray<NSDictionary *> * const recordBanners = x.responseObject[@"record_banners"];
        NSArray<IMYSGRecordBannerConfig *> *allRecordConfigs = [recordBanners toModels:IMYSGRecordBannerConfig.class];
        
        NSArray<NSDictionary *> * const analysisBanners = x.responseObject[@"analysis_banners"];
        NSArray<IMYSGAnalysisBannerConfigRemote *> *allAnalysisConfigs = [analysisBanners toModels:IMYSGAnalysisBannerConfigRemote.class];
        
        imy_asyncMainBlock(^{
            // 更新记录横幅配置
            [self updateAllConfigsWithArray:allRecordConfigs];
            // 更新分析横幅配置
            [[IMYSGAnalysisBannerSDK sharedInstance] updateAllConfigsWithArray:allAnalysisConfigs];
        });
    } error:^(NSError *error) {
        NSLog(@"%@ Request Fails!", self);
        // 2、6、14、30、62
        self.retryDelay = (self.retryDelay + 1) * 2;
        NSString *queueKey = NSStringFromClass(self.class);
        NSString *retryKey = [queueKey stringByAppendingString:@"RetryX"];
        [NSObject imy_asyncBlock:^{
            [self requestWithImmediately:NO];
        } onQueue:dispatch_get_global_queue(0, 0) afterSecond:self.retryDelay forKey:retryKey];
    }];
}

- (void)updateAllConfigsWithArray:(NSArray<IMYSGRecordBannerConfig *> * const)allConfigs {
    // 延迟释放旧对象
    id const holds = self.currentRecordConfigs;
    // 记录项目相关 banner
    self.currentRecordConfigs = allConfigs;
    // 重置重试时间
    self.retryDelay = 0;
    // 1秒后释放
    imy_asyncMainBlock(1, ^{
        [holds class];
    });
    // 更新通知
    [self postNotification];
}

- (void)postNotification {
    imy_asyncMainBlock(^{
        [((RACSubject *)self.loadedSignal) sendNext:self];
    });
}

- (NSArray<IMYSGRecordBannerConfig *> *)allConfigs {
    NSArray * const allConfigs = self.currentRecordConfigs;
    return allConfigs;
}

- (NSArray<IMYSGRecordBannerConfig *> *)configsForFilter:(BOOL (^)(IMYSGRecordBannerConfig *))block {
    if (!block) {
        NSAssert(NO, @"无效的block！");
        return nil;
    }
    NSArray * const allConfigs = self.currentRecordConfigs;
    NSMutableArray *filterConfigs = [NSMutableArray array];
    for (IMYSGRecordBannerConfig *config in allConfigs) {
        if (block(config)) {
            [filterConfigs addObject:config];
        }
    }
    return [filterConfigs copy];
}

- (NSArray<IMYSGRecordBannerConfig *> *)configsForItem:(NSInteger const)item {
    return [self configsForFilter:^BOOL(IMYSGRecordBannerConfig *element) {
        return element.item == item;
    }];
}

@end

#pragma mark - 对外暴露的配置属性

@interface IMYSGRecordBannerConfig ()

@property (nonatomic, assign) NSInteger item;
@property (nonatomic, assign) NSInteger period_type;
@property (nonatomic, copy) NSArray<NSNumber *> *period_stage;
@property (nonatomic, copy) NSArray<NSNumber *> *prepare_preg_stage;
@property (nonatomic, strong) IMYSGRecordBannerConfigRange *baby_month;
@property (nonatomic, strong) IMYSGRecordBannerConfigRange *pregnancy_week;
@property (nonatomic, copy) NSArray<NSNumber *> *analysis_results;
@property (nonatomic, copy) NSArray<NSNumber *> *sub_items;
@property (nonatomic, copy) NSArray<IMYSGRecordBannerConfigRange *> *ranges;
@property (nonatomic, assign) NSInteger vip_material_id;
@property (nonatomic, copy) NSString *image;
@property (nonatomic, copy) NSString *image_night;
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *subtitle;
@property (nonatomic, copy) NSString *uri;
@property (nonatomic, assign) NSInteger expire_days;

@end

@implementation IMYSGRecordBannerConfig

+ (NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"ranges" : IMYSGRecordBannerConfigRange.class,
    };
}

@end

@interface IMYSGRecordBannerConfigRange ()

@property (nonatomic, copy) NSString *min;
@property (nonatomic, copy) NSString *max;

@end

@implementation IMYSGRecordBannerConfigRange

@end
