//
//  IMYRightsURIRegister.m
//  IMYBaseKit
//
//  Created by ljh on 2025/6/25.
//

#import "IMYRightsURIRegister.h"
#import "IMYBaseKit.h"
#import "IMYSubGuideSession.h"
#import "IMYSubGuideManager.h"
#import "IMYRightsSDK.h"
#import "IMYSubGuideVipInfoVC.h"
#import "IMYSubGuideVipInfoDialog.h"
#import "IMYSubGuideVipRightVC.h"
#import "IMYSGRightsDetailPopupsAction.h"
#import "IMYSubGuideUnlockView.h"

@implementation IMYRightsURIRegister

IMY_KYLIN_FUNC_PREMAIN_ASYNC {
    [IMYRightsURIRegister registerRightsSDKUriAction];
    [IMYRightsURIRegister registerSubGuideUriAction];
    [IMYRightsURIRegister registerVIPPreviewURIAction];
    [IMYRightsURIRegister registerPopupsURIAction];
}

IMY_KYLIN_FUNC_MAINTAB_ASYNC {
    /// 注册权益变化
    [[[IMYRightsSDK sharedInstance] loadedSignal] subscribeNext:^(id x) {
        for (IMYVKWebView *webView in IMYVKWebView.allWebViews) {
            [webView sendEvent:@"onRightsChange"];
        }
    }];
}

/// 权益 SDK
+ (void)registerRightsSDKUriAction {
    /// 是否命中订阅实验组
    [[IMYURIManager shareURIManager] addForPath:@"rights/sdk/inABTest" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        BOOL inABTest = [[IMYRightsSDK sharedInstance] inABTest];
        NSDictionary *resultMap = @{
            @"inABTest" : @(inABTest)
        };
        [actionObject callbackWithObject:resultMap];
    }];
    
    /// 刷新权益状态
    [[IMYURIManager shareURIManager] addForPath:@"rights/sdk/refresh" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [[IMYRightsSDK sharedInstance] refreshData];
    }];
    
    /// 是否命中订阅实验组
    [[IMYURIManager shareURIManager] addForPath:@"rights/sdk/detail" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYRightsSDK *rightsSDK = [IMYRightsSDK sharedInstance];
        NSDictionary *resultMap = @{
            @"type" : @(rightsSDK.currentRightsType),
            @"sub_type" : @(rightsSDK.currentSubscribeType),
            @"hasRight": @([rightsSDK hasRightsItemForKey:[actionObject.uri.params objectForKey:@"rightKey"]])
        };
        [actionObject callbackWithObject:resultMap];
    }];
    
    
    // 订阅支付流程
    [[IMYURIManager sharedInstance] addForPath:@"rights/pay" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        // 获取支付参数
        NSInteger const planId = [actionObject.uri.params[@"planId"] integerValue];
        NSInteger const priceId = [actionObject.uri.params[@"priceId"] integerValue];
        NSString * const scene_key = actionObject.uri.params[@"scene_key"];
        NSString * const gift_info = actionObject.uri.params[@"gift_info"];
        NSString * const gift_promotion_ids = actionObject.uri.params[@"gift_promotion_ids"];
        NSInteger const user_promotion_id = [actionObject.uri.params[@"user_promotion_id"] integerValue];
        NSString * const price = actionObject.uri.params[@"price"];
        NSString * const discount_price = actionObject.uri.params[@"discount_price"];
        NSInteger const sub_type = [actionObject.uri.params[@"sub_type"] integerValue];
        NSInteger const quick_order = [actionObject.uri.params[@"quick_order"] integerValue];
        NSInteger const disable_alerts = [actionObject.uri.params[@"disable_alerts"] integerValue];
        
        // 构建临时session
        IMYSubGuideSession *session = [IMYSubGuideSession new];
        session.fromVC = actionObject.getUsingViewController;
        session.sceneKey = scene_key;
        session.planId = planId;
        session.priceId = priceId;
        session.sub_type = sub_type;
        session.gift_info = gift_info;
        session.gift_promotion_ids = [gift_promotion_ids imy_jsonObject];
        session.user_promotion_id = user_promotion_id;
        session.quick_order = quick_order;
        session.disable_alerts = disable_alerts;
        
        // 构建虚假价格包
        IMYSubGuidePricingListItem *fakePricing = [IMYSubGuidePricingListItem new];
        fakePricing.id = priceId;
        fakePricing.discount_price = discount_price;
        fakePricing.price = price;
        fakePricing.sub_type = sub_type;
        session.currentPricing = fakePricing;
        
        // 监听支付回调
        session.onPayCompletedBlock = ^(BOOL isSuccess, BOOL isNewStyle) {
            // 支付完成，回调给前端
            [actionObject callbackWithObject:@{
                @"success" : @(isSuccess),
            }];
        };
        
        // 开始支付
        [[IMYSubGuideManager sharedInstance] payWithSession:session];
    }];
    
    // 获取服务端需要的请求参数
    [[IMYURIManager sharedInstance] addForPath:@"rights/get/analysis_params"
                               withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        // 获取前端想要的类型
        NSInteger const type = [actionObject.uri.params[@"type"] integerValue];
        // 返回结果
        NSMutableDictionary *retParams = [NSMutableDictionary dictionary];
        if (type == 1 || type == 0) {
            // 追加经期参数
            NSDictionary *mensParams = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"rights/get/mens_analysis_params" params:nil];
            retParams[@"mens_analysis_params"] = mensParams.imy_jsonString ?: @"";
        }
        if (type == 2 || type == 0) {
            // 追加备孕参数
            NSDictionary *prepParams = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"rights/get/ovulate_analysis_params" params:nil];
            retParams[@"prep_analysis_params"] = prepParams.imy_jsonString ?: @"";
        }
        [actionObject callbackWithObject:retParams];
    }];
}

/// 订阅引导
+ (void)registerSubGuideUriAction {
    // 订阅引导-支付半弹窗
    [[IMYURIManager shareURIManager] addForPath:@"subscribe/pay/dialog" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary * const options = actionObject.uri.params;
        NSString * const sceneKey = options[@"sceneKey"];
        NSString * const success_redirect_uri = options[@"success_redirect_uri"];
        NSInteger const sub_type_anchor = [options[@"sub_type_anchor"] integerValue];
        
        UIViewController *fromVC = [UIViewController imy_currentViewControlloer];
        
        IMYSubGuideSession *session = [IMYSubGuideSession new];
        session.fromVC = fromVC;
        session.sceneKey = sceneKey;
        session.sub_type_anchor = sub_type_anchor;
        session.onPayCompletedBlock = ^(BOOL isSuccess, BOOL isNewStyle) {
            // 支付成功，支持重定向
            if (!isNewStyle && isSuccess && imy_isNotEmptyString(success_redirect_uri)) {
                imy_asyncMainBlock(^{
                    [[IMYURIManager shareURIManager] runActionWithString:success_redirect_uri];
                });
            }
            
            // 回调支付结果给调用方
            NSLog(@"回调支付结果给调用方: dialog");
            NSMutableDictionary *result = [NSMutableDictionary dictionary];
            [result imy_setNonNilObject:@(isSuccess) forKey:@"isSuccess"];
            if (actionObject.implCallbackBlock) {
                actionObject.implCallbackBlock(result, nil, nil);
            }
        };
        
        if (![IMYNetState networkEnable]) {
            [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
            return;
        }
        
        id<IMYSubGuideVipInfoDialog> dialog = [IMYSubGuideVipInfoDialogFactory dialogWithSession:session
                                                                                         options:options];
        [dialog show];
    }];
    
    // 订阅引导-支付转化页
    [[IMYURIManager shareURIManager] addForPath:@"subscribe/pay/page" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *sceneKey = actionObject.uri.params[@"sceneKey"];
        NSString *success_redirect_uri = actionObject.uri.params[@"success_redirect_uri"];
        NSInteger sub_type_anchor = [actionObject.uri.params[@"sub_type_anchor"] integerValue];
        
        UIViewController *fromVC = [UIViewController imy_currentViewControlloer];
        
        IMYSubGuideSession *session = [IMYSubGuideSession new];
        session.fromVC = fromVC;
        session.sceneKey = sceneKey;
        session.sub_type_anchor = sub_type_anchor;
        session.onPayCompletedBlock = ^(BOOL isSuccess, BOOL isNewStyle) {
            // 支付成功，支持重定向
            if (!isNewStyle && isSuccess && imy_isNotEmptyString(success_redirect_uri)) {
                imy_asyncMainBlock(^{
                    [[IMYURIManager shareURIManager] runActionWithString:success_redirect_uri];
                });
            }
            
            // 回调支付结果给调用方
            NSLog(@"回调支付结果给调用方: page");
            NSMutableDictionary *result = [NSMutableDictionary dictionary];
            [result imy_setNonNilObject:@(isSuccess) forKey:@"isSuccess"];
            if (actionObject.implCallbackBlock) {
                actionObject.implCallbackBlock(result, nil, nil);
            }
        };
        
        IMYSubGuideVipInfoVC *vc = [[IMYSubGuideVipInfoVC alloc] initWithSession:session];
        vc.fromURI = actionObject.uri;
        [fromVC imy_push:vc];
    }];
    
    // 订阅引导-权益详情
    [[IMYURIManager shareURIManager] addForPath:@"sub/rights_info" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *sceneKey = actionObject.uri.params[@"sceneKey"];
        NSString *success_redirect_uri = actionObject.uri.params[@"success_redirect_uri"];
        NSString *group_key = actionObject.uri.params[@"group_key"];
        NSString *image_key = actionObject.uri.params[@"image_key"];
        
        UIViewController *fromVC = [UIViewController imy_currentViewControlloer];
        
        IMYSubGuideSession *session = [IMYSubGuideSession new];
        session.fromVC = fromVC;
        session.sceneKey = sceneKey;
        session.onPayCompletedBlock = ^(BOOL isSuccess, BOOL isNewStyle) {
            // 支付成功，支持重定向
            if (!isNewStyle && isSuccess && imy_isNotEmptyString(success_redirect_uri)) {
                imy_asyncMainBlock(^{
                    [[IMYURIManager shareURIManager] runActionWithString:success_redirect_uri];
                });
            }
            
            // 回调支付结果给调用方
            NSLog(@"回调支付结果给调用方: 权益详情");
            NSMutableDictionary *result = [NSMutableDictionary dictionary];
            [result imy_setNonNilObject:@(isSuccess) forKey:@"isSuccess"];
            if (actionObject.implCallbackBlock) {
                actionObject.implCallbackBlock(result, nil, nil);
            }
        };
        
        // 协议支持定位到指定的 group
        if (imy_isNotEmptyString(group_key)) {
            session.currentRightGroupKey = group_key;
        }
        
        // 协议支持定义到指定的 image
        if (imy_isNotEmptyString(image_key)) {
            session.currentRightImageKey = image_key;
        }
        
        IMYSubGuideVipRightVC *vc = [[IMYSubGuideVipRightVC alloc] initWithSession:session];
        vc.fromURI = actionObject.uri;
        [fromVC imy_push:vc];
    }];
    
    // 显示解锁组件
    [[IMYURIManager sharedInstance] addForPath:@"subscribe/unlock/show" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UIViewController * const pageVC = actionObject.getUsingViewController;
        NSString * const sceneKey = actionObject.uri.params[@"sceneKey"];
        BOOL const ignorePopupsRequest = [actionObject.uri.params[@"ignorePopups"] integerValue] == 1;
        BOOL hasUnlockView = NO;
        for (UIView *subview in pageVC.view.subviews) {
            if ([subview isKindOfClass:IMYSubGuideUnlockView.class]) {
                hasUnlockView = YES;
                break;
            }
        }
        if (!sceneKey.length || hasUnlockView) {
            [actionObject callbackWithObject:@{
                @"error" : hasUnlockView ? @"has unlock view" : @"scene key is empty",
            }];
            return;
        }
        
        IMYSubGuideConfig *config = [[IMYSubGuideConfig alloc] init];
        config.scene_key = sceneKey;
        config.ignorePopupsRequest = ignorePopupsRequest;
        [config setRefreshedBlock:^(CGFloat height) {
            [actionObject callbackWithObject:@{
                @"height" : @(height),
            } error:nil eventName:@"subscribe/unlock/refreshed"];
        }];
        
        IMYSubGuideUnlockView *unlockView = [IMYSubGuideUnlockView unlockViewWithConfig:config];
        [pageVC.view addSubview:unlockView];
        [unlockView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.equalTo(pageVC.view);
            make.bottom.equalTo(pageVC.view.mas_bottom).offset(pageVC.view.bounds.origin.y);
        }];
        
        [actionObject callbackWithObject:@{
            @"height" : @(unlockView.imy_height),
        }];
        
        // 监听会员变化
        @weakify(unlockView);
        [[[IMYRightsSDK sharedInstance].loadedSignal takeUntil:unlockView.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
            @strongify(unlockView);
            if (IMYRightsSDK.sharedInstance.currentRightsType != IMYRightsTypeNone && unlockView.superview) {
                // 已有会员，需要隐藏该解锁组件
                [unlockView removeFromSuperview];
                // 发送高度变化通知
                [actionObject callbackWithObject:@{
                    @"height" : @0,
                } error:nil eventName:@"subscribe/unlock/refreshed"];
            }
        }];
    }];
    
    // 移除所有解锁组件
    [[IMYURIManager sharedInstance] addForPath:@"subscribe/unlock/hide" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UIViewController * const pageVC = actionObject.getUsingViewController;
        for (UIView *subview in pageVC.view.subviews) {
            if ([subview isKindOfClass:IMYSubGuideUnlockView.class]) {
                [subview removeFromSuperview];
            }
        }
        [actionObject callbackWithObject:@{
            @"height" : @0,
        } error:nil eventName:@"subscribe/unlock/refreshed"];
    }];
}

+ (NSMapTable<UIViewController *, IMYSGRightsDetailPopupsAction *> *)sharedRightsPagePopupsMap {
    static NSMapTable *map = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        map = [NSMapTable weakToStrongObjectsMapTable];
    });
    return map;
}

+ (void)registerPopupsURIAction {
    // 注册页面进入的弹窗流程
    [[IMYURIManager sharedInstance] addForPath:@"subscribe/rightsPage/enter"
                               withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        // 获取场景标识
        NSString * const sceneKey = actionObject.uri.params[@"sceneKey"];
        BOOL const canShow = [actionObject.uri.params[@"canShowEnterDialog"] integerValue] == 1;
        UIViewController * const pageVC = actionObject.getUsingViewController;
        
        if (!sceneKey.length) {
            // 无场景标识
            return;
        }
        
        // 创建弹窗动作对象
        IMYSGRightsDetailPopupsAction *rightsPopups = [IMYSGRightsDetailPopupsAction new];
        rightsPopups.sceneKey = sceneKey;
        rightsPopups.pageVC = pageVC;
        [rightsPopups startLoading];
        
        // 需要标记下，无页面弹窗
        [IMYAlertShowManager setPageStatus:IMYAlertPageStatusNone forPageVC:pageVC];
        
        // 触发页面进入弹窗逻辑
        if (canShow) {
            [rightsPopups onPageInAction];
        }
        
        // 绑定生命周期
        [[self sharedRightsPagePopupsMap] setObject:rightsPopups forKey:pageVC];
    }];
    
    // 注册页面退出时的弹窗展示
    [[IMYURIManager sharedInstance] addForPath:@"subscribe/rightsPage/finish"
                               withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        // 获取页面的弹窗管理对象
        UIViewController * const pageVC = actionObject.getUsingViewController;
        IMYSGRightsDetailPopupsAction *rightsPopups = [[self sharedRightsPagePopupsMap] objectForKey:pageVC];
        if (!rightsPopups) {
            // 无弹窗对象
            [actionObject callbackWithObject:@{
                @"canFinish" : @1,
            }];
            return;
        }
        
        // 触发页面退出事件
        __block BOOL isCallbacked = NO;
        [rightsPopups onPageOutAction:^{
            // 告知前端执行页面返回
            if (!isCallbacked) {
                isCallbacked = YES;
                [actionObject callbackWithObject:@{
                    @"canFinish" : @1,
                }];
            }
        }];
        
        // 等待1秒，判断是否有退出回调
        imy_asyncMainBlock(1, ^{
            // 告知前端执行页面无返回
            if (!isCallbacked) {
                isCallbacked = YES;
                [actionObject callbackWithObject:@{
                    @"canFinish" : @0,
                }];
            }
        });
    }];
    
    // 注册页面滚动事件
    [[IMYURIManager sharedInstance] addForPath:@"subscribe/rightsPage/scrollStateChanged"
                               withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        // 获取页面的弹窗管理对象
        UIViewController * const pageVC = actionObject.getUsingViewController;
        IMYSGRightsDetailPopupsAction *rightsPopups = [[self sharedRightsPagePopupsMap] objectForKey:pageVC];
        if (!rightsPopups) {
            // 无弹窗对象
            return;
        }
        // 判断滚动状态
        BOOL isScrolling = [actionObject.uri.params[@"scrollState"] integerValue] == 1;
        if (isScrolling) {
            [rightsPopups onPageDidScrollAction:CGPointMake(0, 1)];
        }
    }];
}

+ (void)registerVIPPreviewURIAction {
    // VIP 预览
    [[IMYURIManager sharedInstance] addForPath:@"debug/vip/preview" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        // 过滤短时间内的重复调用
        static BOOL kDebugVIPÇalling = NO;
        if (kDebugVIPÇalling) {
            return;
        }
        kDebugVIPÇalling = YES;
        imy_asyncMainBlock(1, ^{
            kDebugVIPÇalling = NO;
        });
        // 获取注入参数
        NSString * const gotoURI = actionObject.uri.params[@"uri"];
        NSString * const appendParams = actionObject.uri.params[@"preview_params"];
        // 跳转到落地页
        if (gotoURI.length > 0) {
            imy_asyncMainBlock(0.3, ^{
                [[IMYURIManager sharedInstance] runActionWithString:gotoURI];
            });
        }
        // 配置追加的参数
        if (appendParams.length > 0) {
            xVIPAppendHeaderValue = [appendParams copy];
        } else {
            xVIPAppendHeaderValue = nil;
        }
        // 会员预览标记
        [[IMYKV defaultKV] setBool:appendParams.length > 0 forKey:@"debug/vip/preview"];
        // 全局进行接口刷新 (由 Rights->具体页面 )
        [[IMYRightsSDK sharedInstance] refreshData];
    }];
}

// 注入的会员请求头
static NSString *xVIPAppendHeaderValue = nil;

IMY_KYLIN_FUNC_PREMAIN {
    // 注入请求头
    [IMYMeetyouHTTPHooks addHeadersHook:^NSDictionary *(NSString *urlString) {
        // 有预览请求头 + 会员域名
        if (xVIPAppendHeaderValue.length > 0 && [urlString containsString:@"sub.seeyouyima.com"]) {
            return @{
                @"X-Resource-Preview" : xVIPAppendHeaderValue,
            };
        }
        return nil;
    } forKey:@"debug/vip/preview"];
    
    // 退到后台则清空配置
    [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidEnterBackgroundNotification object:nil queue:nil usingBlock:^(NSNotification * _Nonnull notification) {
        // 重置会员预览标记
        if (xVIPAppendHeaderValue != nil) {
            xVIPAppendHeaderValue = nil;
            imy_asyncBlock(^{
                [[IMYKV defaultKV] setBool:NO forKey:@"debug/vip/preview"];
            });
        }
    }];
}

@end

