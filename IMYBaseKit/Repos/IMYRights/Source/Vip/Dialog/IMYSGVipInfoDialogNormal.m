//
//  IMYSGVipInfoDialogNormal.m
//  IMYBaseKit
//
//  Created by ljh on 2025/4/9.
//

#import "IMYSGVipInfoDialogNormal.h"
#import "IMYBaseKit.h"
#import "IMYSubGuideManager.h"
#import "IMYSubGuideVipPriceCell.h"
#import "IMYSubGuideVipInfo.h"
#import "IMYSubGuideCheckPrivateView.h"
#import "IMYSubGuideProtocolReadView.h"
#import "IMYSubGuideVipGiftInfoView.h"
#import "IMYSubGuideDialogRecommendView.h"
#import <IOC-Protocols/IOCAppMainTabVC.h>
#import "IMYSubGuidePopupsInfoView.h"
#import "IMYSubGuideRPAnimationView.h"
#import "IMYSubGuidePayRetView.h"
#import <IOC-Protocols/IOCAppInfo.h>

@interface IMYSGVipInfoDialogNormal () <UICollectionViewDelegate, UICollectionViewDataSource, UIGestureRecognizerDelegate>

@property (nonatomic, strong, readonly) IMYSubGuideSession *session;
@property (nonatomic, copy, readonly) NSDictionary *options;

@property (nonatomic, strong) IMYSubGuideVipInfo *vipInfo;
@property (nonatomic, strong) IMYSubGuidePricingListItem *currentPricing;
@property (nonatomic, assign) NSInteger lastSelectedPricingType;

@property (nonatomic, weak) UIViewController *currentShowVC;
@property (nonatomic, weak) UIView *currentShowRootView;

/// 选中价格的偏移坐标
@property (nonatomic, assign) CGFloat currentPricingOffsetX;

@property (nonatomic, assign) BOOL hasChecked; ///< 是否已经自动锚定
@property (nonatomic, assign) BOOL isCanExpos; ///< 是否可以开启曝光检测（默认 NO，首次锚定结束延迟 0.1 秒 YES）

@property (nonatomic, assign) BOOL isDismissed;

@property (nonatomic, strong) UIView *maskControl;
@property (nonatomic, strong) UIView *cardView;

@property (nonatomic, strong) CALayer *cardWhiteLayer;
@property (nonatomic, strong) CAGradientLayer *cardGradientLayer;

@property (nonatomic, strong) UIPanGestureRecognizer *panGesture;
@property (nonatomic, assign) CGFloat panMinY;
@property (nonatomic, assign) CGFloat panMaxY;
@property (nonatomic, assign) CGFloat panVelocityY;
@property (nonatomic, assign) BOOL isPanToUp;

@property (nonatomic, strong) UIView *topBar;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) IMYSubGuideDialogRecommendView *recommendView;

@property (nonatomic, strong) IMYTouchEXButton *closeBtn;
@property (nonatomic, strong) UICollectionView *priceCollectionView;
@property (nonatomic, strong) IMYSubGuideVipGiftInfoView *giftInfoView;
@property (nonatomic, strong) UILabel *descLabel;
@property (nonatomic, strong) IMYCapsuleButton *payBtn;
@property (nonatomic, strong) IMYSubGuideCheckPrivateView *checkPrivateView;

@property (nonatomic, strong) IMYSubGuidePopupsInfoView *popupInfoView;

/// 存在数据错误，不能显示
@property (nonatomic, assign) BOOL isDataError;

/// 是否处于继续支付状态
@property (nonatomic, assign) BOOL isResumePaying;

/// 卡片样式，0：标准版，1：大卡片
@property (nonatomic, assign, readonly) NSInteger priceCardStyle;

@end

@implementation IMYSGVipInfoDialogNormal

- (instancetype)initWithSession:(IMYSubGuideSession *)session options:(NSDictionary *)options {
    self = [super initWithFrame:[UIScreen mainScreen].bounds];
    if (self) {
        _session = session ?: [IMYSubGuideSession new];
        _options = [options copy];
        
        // 价格卡片样式
        IMYABTestExperiment *exp = [[IMYABTestManager sharedInstance] experimentForKey:@"PriceIsBigCard"];
        if ([exp.vars integerForKey:@"PriceCard"] == 1) {
            _priceCardStyle = 1;
        } else {
            _priceCardStyle = 0;
        }
        
#ifdef DEBUG
        if (imy_isEmptyString(self.session.sceneKey)) {
            imy_asyncMainBlock(^{
                [UIAlertController imy_quickAlert:@"警告 sceneKey 没传，请联系开发"];
            });
        }
#endif
        
        self.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
        self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
        
        self.maskControl.frame = self.bounds;
        [self addSubview:self.maskControl];
        
        self.cardView.frame = CGRectMake(0, self.imy_bottom, self.imy_width, 300);
        [self addSubview:self.cardView];
        
        // 不能用懒加载，必须在主线程初始化
        self.popupInfoView = [IMYSubGuidePopupsInfoView new];
        self.popupInfoView.pageEntryType = 2;
        
        [self setupObservers];
        [self setupPanGesture];
        [self setupTopBar];
        [self setupContentView];
        [self setupRecommendView];
        
        // 总卡片高度
        self.cardView.imy_height = self.recommendView.imy_bottom + (self.recommendView.imy_height > 0 ? 0 : -8) + SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
        
        // 加载数据
        [self loadData];
    }
    return self;
}

- (void)setupObservers {
    @weakify(self);
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:KRightsSubGuideRaymentResultNotification object:nil] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification * _Nullable x) {
        imy_asyncMainExecuteBlock(^{
            @strongify(self);
            NSDictionary *obj = x.object;
            if (obj && [obj isKindOfClass:[NSDictionary class]]) {
                BOOL isSuccess = [obj[@"isSuccess"] boolValue];
                if (isSuccess) {
                    [self dismiss];
                } else if ([obj[@"isOfferExpires"] boolValue]) {
                    // 优惠券过期，强制刷新支付半弹窗
                    [self loadData];
                }
            }
        });
    }];
    
    RACSignal *promotionChangedSingle = [[NSNotificationCenter defaultCenter] rac_addObserverForName:KRightsSubGuidePromotionChangedNotification object:nil];
    [[[promotionChangedSingle takeUntil:self.rac_willDeallocSignal] throttle:0.2] subscribeNext:^(IMYSubGuidePricingListItem * const x) {
        @strongify(self);
        // 如果是试用价格包，刷新后会消失，需要重新滚到新锚定区域
        if (x == self.currentPricing && x.real_discount_price.doubleValue <= 0) {
            self.lastSelectedPricingType = 0;
            self.hasChecked = NO;
        }
        [self loadData];
    }];
    
    RACSignal *animStartSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:K_Noti_SubGuid_RedPacket_Animation_Start object:nil];
    [[[animStartSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *x) {
        @strongify(self);
        NSInteger const priceId = [x.userInfo[@"priceId"] integerValue];
        if (priceId != self.currentPricing.id) {
            return;
        }
        // 有红包动画，显示折扣前的价格
        [self refreshPayBtnShowText:self.currentPricing.price];
    }];
    
    RACSignal *pngOverSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:K_Noti_SubGuid_RedPacket_AnimationPNG_Over object:nil];
    [[[pngOverSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *x) {
        @strongify(self);
        NSInteger const priceId = [x.userInfo[@"priceId"] integerValue];
        if (priceId != self.currentPricing.id) {
            return;
        }
        NSString *priceText = self.currentPricing.price;
        NSString *discountPriceText = self.currentPricing.real_discount_price;
        if ([priceText imy_isPureInt] && [discountPriceText imy_isPureNumber]) {
            NSInteger priceNum = priceText.integerValue;
            NSInteger discountPriceNum = discountPriceText.integerValue + ([discountPriceText imy_isPureInt] ? 0 : 1);
            if (priceNum > discountPriceNum) {
                // 有价格变动动画，先显示原价
                [self refreshPayBtnShowText:priceText];
                [self resetPayBtnShowTextWithAnimationEnd];
                return;
            }
        }
        // 无价格变动动画
        [self refreshPayBtnShowText:nil];
    }];
    
    RACSignal *animPricingSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:K_Noti_SubGuid_RedPacket_Animation_Pricing object:nil];
    [[[animPricingSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification * _Nullable x) {
        @strongify(self);
        NSInteger const priceId = [x.userInfo[@"priceId"] integerValue];
        if (priceId != self.currentPricing.id) {
            return;
        }
        NSString *priceText = x.object;
        [self refreshPayBtnShowText:priceText];
        [self resetPayBtnShowTextWithAnimationEnd];
    }];
    
    RACSignal *pricingChangedSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:K_Noti_SubGuid_RedPacket_Pricing_Changed object:nil];
    [[[pricingChangedSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification * _Nullable x) {
        @strongify(self);
        NSInteger const priceId = [x.userInfo[@"priceId"] integerValue];
        if (priceId != self.currentPricing.id) {
            return;
        }
        [self refreshPayBtnShowText:nil];
    }];
}

- (void)setupPanGesture {
    self.panGesture = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePanGesture:)];
    self.panGesture.maximumNumberOfTouches = 1;
    self.panGesture.delegate = self;
    self.panGesture.enabled = NO;
    [self.cardView addGestureRecognizer:self.panGesture];
}

- (void)setupTopBar {
    self.topBar = [UIView new];
    self.topBar.frame = CGRectMake(0, 0, SCREEN_WIDTH, 44);
    if (self.priceCardStyle == 1) {
        // 大卡片样式
        [self.topBar imy_addThemeChangedBlock:^(UIView *weakObject) {
            if (IMYPublicAppHelper.shareAppHelper.isNight) {
                weakObject.backgroundColor = nil;
            } else {
                weakObject.backgroundColor = UIColor.whiteColor;
            }
        }];
    }
    [self.cardView addSubview:self.topBar];
    
    self.closeBtn.frame = CGRectMake(0, 12, 20, 20);
    self.closeBtn.imy_right = self.topBar.imy_width - 12;
    [self.topBar addSubview:self.closeBtn];
    
    UIImageView *iconView = [UIImageView new];
    iconView.frame = CGRectMake(12, 10, 94, 24);
    [iconView imy_setImage:@"dyzf_icon_vip"];
    [self.topBar addSubview:iconView];
}

- (void)setupContentView {
    self.contentView = [UIView new];
    if (self.priceCardStyle == 1) {
        self.contentView.frame = CGRectMake(0, 44, SCREEN_WIDTH, 300);
    } else {
        [self.contentView imy_setBackgroundColor:kCK_White_AN];
        [self.contentView imy_drawAllCornerRadius:12];
        self.contentView.frame = CGRectMake(12, 52, SCREEN_WIDTH - 24, 300);
    }
    [self.cardView addSubview:self.contentView];
    
    self.priceCollectionView.frame = CGRectMake(0, 18, self.contentView.imy_width, [IMYSubGuideVipPriceCell showItemSizeWithStyle:self.priceCardStyle].height);
    [self.contentView addSubview:self.priceCollectionView];
    
    self.giftInfoView.frame = CGRectMake(12, self.priceCollectionView.imy_bottom + 8, self.contentView.imy_width - 24, 0);
    [self.contentView addSubview:self.giftInfoView];
    
    self.descLabel.alpha = 0;
    self.descLabel.frame = CGRectMake(12, self.giftInfoView.imy_bottom - 8, self.contentView.imy_width - 24, 0);
    [self.contentView addSubview:self.descLabel];
    
    // payBtn 先隐藏，请求数据成功再展示
    self.payBtn.alpha = 0;
    self.payBtn.frame = CGRectMake(12, self.descLabel.imy_bottom + 8, self.contentView.imy_width - 24, 48);
    [self.contentView addSubview:self.payBtn];
    
    self.checkPrivateView.frame = CGRectMake(12, self.payBtn.imy_bottom + 8, self.contentView.imy_width - 24, self.checkPrivateView.imy_height);
    [self.contentView addSubview:self.checkPrivateView];
    
    // 默认中间内容区高度
    self.contentView.imy_height = self.checkPrivateView.imy_bottom + 18;
    
    // 渐变区颜色
    CGFloat const cardWhiteHeight = self.payBtn.imy_top - 8;
    self.cardWhiteLayer.frame = CGRectMake(0, self.contentView.imy_top, self.cardView.imy_width, cardWhiteHeight);
    self.cardGradientLayer.frame = CGRectMake(0, CGRectGetMaxY(self.cardWhiteLayer.frame), self.cardView.imy_width, self.contentView.imy_height - cardWhiteHeight);
    
    // 埋点
    @weakify(self);
    self.payBtn.imyut_eventInfo.eventName = [NSString stringWithFormat:@"IMYSubGuideVipInfoDialog-payBtn-%p", self];
    [self.payBtn.imyut_eventInfo setShouldExposureDetectingBlock:^BOOL(__kindof UIView *view) {
        @strongify(self);
        return self.isCanExpos;
    }];
    [self.payBtn.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        // [dy_qrxybzfan]：订阅_确认协议并支付按钮
        [self biReportWithAction:1 eventName:@"dy_qrxybzfan" pricing:self.currentPricing];
    }];
}

- (void)setupRecommendView {
    self.recommendView = [IMYSubGuideDialogRecommendView new];
    self.recommendView.frame = CGRectMake(0, self.contentView.imy_bottom, SCREEN_WIDTH, 0);
    [self.cardView addSubview:self.recommendView];
}

- (void)refreshAllViews {
    [UIView animateWithDuration:0.15 animations:^{
        [self refreshAllViewsInAnimation];
    }];
}

- (void)refreshAllViewsInAnimation {
    // 赠礼
    self.giftInfoView.sceneKey = self.session.sceneKey;
    [self.giftInfoView updateWithCurrentPricing:self.currentPricing];
    [self.giftInfoView updateWithArrowX:self.currentPricingOffsetX];
    
    // desc
    {
        NSAttributedString * const descAttr = [self.currentPricing getAttributedStringByDesc];
        self.descLabel.alpha = (descAttr.length > 0 ? 1 : 0);
        [self.descLabel imy_addThemeActionBlock:^(UILabel *weakObject) {
            weakObject.textColor = IMY_COLOR_KEY(kCK_Black_B);
            weakObject.attributedText = descAttr;
        } forKey:@"setTextColor"];
        CGFloat offsetY = 8;
        CGFloat height = 16;
        if (self.giftInfoView.alpha < 0.01) {
            offsetY -= 8;
        }
        if (self.descLabel.alpha < 0.01) {
            offsetY -= 8;
            height = 0;
        } else {
            CGFloat const boxWidth = (self.priceCardStyle == 1 ? SCREEN_WIDTH - 24 : SCREEN_WIDTH - 48);
            self.descLabel.imy_width = boxWidth;
            [self.descLabel imy_sizeToFitHeight];
            height = MAX(16, self.descLabel.imy_height);
        }
        self.descLabel.imy_top = self.giftInfoView.imy_bottom + offsetY;
        self.descLabel.imy_height = height;
    }
    
    // 支付按钮
    self.payBtn.imy_top = self.descLabel.imy_bottom + 8;
    
    // 协议刷新
    [self.checkPrivateView refreshWithType:self.currentPricing.type];
    self.checkPrivateView.imy_top = self.payBtn.imy_bottom + 8;
    
    // 中间内容区高度
    self.contentView.imy_height = self.checkPrivateView.imy_bottom + 18;
    
    // 渐变区颜色
    CGFloat const cardWhiteHeight = self.payBtn.imy_top - 8;
    self.cardWhiteLayer.frame = CGRectMake(0, self.contentView.imy_top, self.cardView.imy_width, cardWhiteHeight);
    self.cardGradientLayer.frame = CGRectMake(0, CGRectGetMaxY(self.cardWhiteLayer.frame), self.cardView.imy_width, self.contentView.imy_height - cardWhiteHeight);
    
    // 推荐区
    CGFloat minY = SCREEN_STATUSBAR_HEIGHT + 10;
    self.recommendView.imy_top = self.contentView.imy_bottom;
    self.recommendView.maxHeight = SCREEN_HEIGHT - minY - self.recommendView.imy_top;
    [self.recommendView setupWithData:self.vipInfo.dialog_group_list];
    
    // 总卡片高度
    self.cardView.imy_height = self.recommendView.imy_bottom + SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
    
    // 滑动区
    self.panMinY = MAX(self.imy_bottom - self.cardView.imy_height, minY);
    if (self.recommendView.imy_height > 0) {
        self.panMaxY = SCREEN_HEIGHT - (self.contentView.imy_bottom + 8 + 66);
        self.panGesture.enabled = YES;
    } else {
        self.panMaxY = SCREEN_HEIGHT - (self.contentView.imy_bottom + SCREEN_TABBAR_SAFEBOTTOM_MARGIN);
        self.panGesture.enabled = NO;
    }
    // 推荐区能完整显示，则不用支持滚动
    self.recommendView.tableView.contentOffset = CGPointZero;
    if (self.cardView.imy_height <= SCREEN_HEIGHT - self.panMinY) {
        self.recommendView.tableView.scrollEnabled = NO;
    } else {
        self.recommendView.tableView.scrollEnabled = YES;
    }
    
    // 已经在显示状态，并且动画结束
    if (self.cardView.imy_top != self.imy_bottom) {
        if (self.isPanToUp) {
            // 之前处于吸顶状态，后续变化也需要直接吸顶
            self.cardView.imy_top = self.panMinY;
        } else {
            // 非吸顶状态，可视区需要完整可见
            self.cardView.imy_top = self.panMaxY;
        }
    }
}

- (void)show {
    if (self.superview) {
        return;
    }
    
    if (!self.currentShowVC) {
        // 为了 present 可以盖住对应支付半弹窗，所以只能加到对应 VC.view 上
        UIViewController *rootVC = [UIApplication sharedApplication].delegate.window.rootViewController;
        self.currentShowVC = [rootVC imy_currentShowViewController];
        
        // currentShowVC 有 TabBar，加到 tabVC.view 上
        // currentShowVC 有 navVC，加到 navVC.view 上
        // 否则加到 currentShowVC.view 上
        UIView *rootView = nil;
        if (self.currentShowVC.tabBarController.tabBar.window != nil) {
            rootView = self.currentShowVC.tabBarController.view;
        } else if (self.currentShowVC.navigationController.view != nil) {
            rootView = self.currentShowVC.navigationController.view;
        } else {
            rootView = self.currentShowVC.view;
        }
        
        for (UIView *subview in rootView.subviews) {
            if ([subview conformsToProtocol:@protocol(IMYSubGuideVipInfoDialog)]) {
                // 移除老的支付半弹窗
                [subview removeFromSuperview];
            }
        }
        self.currentShowRootView = rootView;
        
        // 监听当前显示VC是否还在显示，不显示则隐藏支付半弹窗
        if ([self.currentShowVC isKindOfClass:IMYPublicBaseViewController.class]) {
            __weak IMYPublicBaseViewController *myVC = self.currentShowVC;
            @weakify(self);
            [[[RACObserve(myVC, isViewWillAppeared) skip:1] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id _Nullable x) {
                @strongify(self);
                if (myVC.isViewWillDisappear) {
                    // vc disappear
                    return;
                }
                if (self.isDismissed) {
                    return;
                }
                imy_asyncMainBlock(^{
                    if (self && myVC.view.superview) {
                        // 视图发生动画中，加到中间过程View中
                        [myVC.view.superview addSubview:self];
                        self.layer.zPosition = 1;
                        self.alpha = 1;
                    }
                });
            }];
            
            static UIView *kHoldDialog = nil;
            [[[RACObserve(myVC, isViewActived) skip:1] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id _Nullable x) {
                @strongify(self);
                if (!self) {
                    return;
                }
                if (self.isDismissed) {
                    return;
                }
                if (!myVC.isViewWillDisappear) {
                    // 返回显示中
                    [rootView addSubview:self];
                    self.alpha = 1;
                    kHoldDialog = nil;
                    // 延迟重新设置一次，避免 UITabBar 再改回去
                    imy_asyncMainBlock(^{
                        [self.superview bringSubviewToFront:self];
                        self.layer.zPosition = 0;
                    });
                } else {
                    // 页面被push
                    self.alpha = 0;
                    kHoldDialog = self;
                }
            }];
        }
    }
    
    // 显示新的支付半弹窗
    UIView *rootView = self.currentShowRootView;
    [rootView addSubview:self];
    
    self.frame = rootView.bounds;
    self.maskControl.frame = self.bounds;
    
    self.alpha = 1;
    self.isDismissed = NO;
    self.isPanToUp = NO;
    
    self.cardView.imy_top = self.imy_bottom;
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.0];
    
    self.maskControl.userInteractionEnabled = NO;
    [UIView animateWithDuration:0.15 animations:^{
        self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
        if (self.panMaxY > 0) {
            self.cardView.imy_top = self.panMaxY;
        } else {
            self.cardView.imy_top = self.imy_bottom - self.cardView.imy_height;
        }
    } completion:^(BOOL finished) {
        self.maskControl.userInteractionEnabled = YES;
        if (self.isDataError) {
            // 接口错误，关闭自己
            [self dismiss];
        }
    }];
}

- (void)dismiss {
    self.isDismissed = YES;
    if (!self.superview) {
        return;
    }
    
    [UIView animateWithDuration:0.15 animations:^{
        self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.0];
        self.cardView.imy_top = self.imy_bottom;
    } completion:^(BOOL finished) {
        self.alpha = 0;
        [self removeFromSuperview];
    }];
    
    // 发送支付页面返回通知
    [[NSNotificationCenter defaultCenter] postNotificationName:kIMYVIPPaymentViewGobackNotificationName
                                                        object:nil];
}

#pragma mark - Data

- (void)loadData {
    NSMutableDictionary *options = [NSMutableDictionary dictionary];
    // 来源页面
    options[@"page_key"] = @"PAY_WINDOW";
    // 锚点参数
    NSInteger const sub_type_anchor = self.session.sub_type_anchor;
    if (sub_type_anchor > 0) {
        options[@"sub_type_anchor"] = @(sub_type_anchor);
    }
    // 当前孕周
    NSInteger const currentPregnenyWeek = IMYHIVE_BINDER(IOCAppInfo).currentPregnenyWeek;
    if (currentPregnenyWeek >= 0) {
        options[@"pregnancy_week"] = @(currentPregnenyWeek);
    }
    // 当前最小宝宝出生日
    NSString * const birthdayString = [IMYHIVE_BINDER(IOCAppInfo).currentLatestBabyBirthday imy_getOnlyDateString];
    if (birthdayString.length > 0) {
        options[@"min_baby_birthday"] = birthdayString;
    }
    @weakify(self);
    [[IMYSubGuideManager sharedInstance] loadVipInfoWithSceneKey:self.session.sceneKey
                                                         options:options
                                                         success:^(IMYSubGuideVipInfo * _Nonnull vipInfo) {
        @strongify(self);
        [self handleVipInfo:vipInfo];
    } error:^(NSError * _Nonnull error) {
        imy_asyncMainBlock(^{
            @strongify(self);
            if (!self.vipInfo) {
                NSString *message = [error.af_responseData imy_jsonObject][@"message"];
                if (!message.length) {
                    message = [IMYNetState networkEnable] ? @"网络缓慢，请稍后再试" : @"网络不见了，请检查网络";
                }
                [UIView imy_showTextHUD:message];
                self.isDataError = YES;
                // 已经显示完毕，直接dismiss
                if (self.maskControl.userInteractionEnabled) {
                    [self dismiss];
                }
            }
            self.isResumePaying = NO;
        });
    }];
}

- (void)handleVipInfo:(IMYSubGuideVipInfo * const)vipInfo {
    self.vipInfo = vipInfo;
    self.session.planId = vipInfo.sub_plan_id;
    
    // [自动锚定价格] 有 checked 选 checked
    IMYSubGuidePricingListItem *currentPricing = nil;
    for (IMYSubGuidePricingListItem *pricing in self.vipInfo.pricing_list) {
        // 有历史选中价格包，在刷新数据时，需要保留当前价格包选中状态
        if (self.lastSelectedPricingType > 0) {
            if (self.lastSelectedPricingType == pricing.sub_type) {
                currentPricing = pricing;
                break;
            }
        } else if (pricing.checked) {
            currentPricing = pricing;
            break;
        }
    }
    // [自动锚定价格] 没有就选第一个
    if (!currentPricing) {
        currentPricing = [self.vipInfo.pricing_list firstObject];
    }
    
    self.currentPricing = currentPricing;
    self.session.currentPricing = currentPricing;
    self.session.priceId = currentPricing.id;
    self.session.sub_type = currentPricing.sub_type;
    self.lastSelectedPricingType = currentPricing.sub_type;
    
    // 加载弹窗
    [self loadPopupsInfoWithRefresh:NO];
    
    @weakify(self);
    imy_asyncMainBlock(^{
        @strongify(self);
        // 内容更新
        [self refreshAllViews];
        // 刷新支付按钮文案
        [self refreshPayBtnShowText:nil];
    });
    
    imy_asyncMainBlock(^{
        @strongify(self);
        [self.priceCollectionView reloadData];
        self.payBtn.alpha = 1;
        
        // [自动锚定价格] 执行自动锚定
        if (!self.hasChecked) {
            NSLog(@"执行自动锚定");
            self.hasChecked = YES;
            NSIndexPath *indexPath = [self indexOfPricing:self.currentPricing inPricings:self.vipInfo.pricing_list];
            if (indexPath && [self.priceCollectionView numberOfItemsInSection:indexPath.section] > indexPath.item) {
                [self.priceCollectionView scrollToItemAtIndexPath:indexPath
                                                 atScrollPosition:UICollectionViewScrollPositionCenteredHorizontally
                                                         animated:NO];
            }
        }
        
        imy_asyncMainBlock(0.1, ^{
            @strongify(self);
            // 可曝光
            self.isCanExpos = YES;
            // 修正角标位置
            [self refreshCurrentPricingOffsetX];
            // 继续支付
            if (self.isResumePaying) {
                imy_asyncMainBlock(0.1, ^{
                    @strongify(self);
                    [self doPayBtnAction];
                });
            }
            self.isResumePaying = NO;
            
            // 更新显示的弹窗
            [self updateShowingPopupInfoView];
        });
    });
}

- (void)loadPopupsInfoWithRefresh:(BOOL const)isRefresh {
    // 刷新用户选中的价格包
    if (isRefresh || self.popupInfoView.currentPricing.id == self.currentPricing.id) {
        self.popupInfoView.currentPricing = self.currentPricing;
    }
    // 判断是否需要初始化
    if (!isRefresh && !self.popupInfoView.onConfirmCompletedBlock) {
        @weakify(self);
        self.popupInfoView.onConfirmCompletedBlock = ^(BOOL const hasPopupInfo) {
            @strongify(self);
            if (!hasPopupInfo) {
                return;
            }
            [self realShowPopupInfoView];
            // 如果当前无价格报，则需要刷新vip_info
            BOOL const containPricingID = [self.popupInfoView.popupValidPriceIds bk_any:^BOOL(NSNumber *pid) {
                for (IMYSubGuidePricingListItem *pricing in self.vipInfo.pricing_list) {
                    if (pid.integerValue == pricing.id) {
                        return YES;
                    }
                }
                return NO;
            }];
            if (!containPricingID) {
                self.lastSelectedPricingType = 0;
                self.hasChecked = NO;
                [self loadData];
            }
        };
        // 弹窗价格包是否存在
        self.popupInfoView.onShouldConfirmBlock = ^BOOL(NSInteger const popupPricingId) {
            @strongify(self);
            if (popupPricingId > 0) {
                for (IMYSubGuidePricingListItem *pricing in self.vipInfo.pricing_list) {
                    if (popupPricingId == pricing.id) {
                        return YES;
                    }
                }
            }
            return NO;
        };
        // 弹窗动效结束，刷新接口，0：确认按钮，1：关闭按钮，2：离开按钮
        self.popupInfoView.onDismissedBlock = ^(const NSInteger actionType) {
            @strongify(self);
            // 取消支付弹窗，用户点击继续支付
            if (self.popupInfoView.userTriggerScene == IMYSGRPopupsTriggerScenePaymentCancel && actionType == 0) {
                // 弹窗价格包 跟 支付的价格包一致
                if (self.popupInfoView.popupPricingId == self.session.priceId || self.popupInfoView.popupValidPriceIds.firstObject.integerValue == self.session.priceId) {
                    // 刷新后继续支付
                    self.isResumePaying = YES;
                }
            }
            // 刷新数据
            [self loadData];
        };
        // 请求所有弹窗配置
        self.popupInfoView.session = self.session;
        self.popupInfoView.currentPricing = self.currentPricing;
        [self.popupInfoView startLoading];
    }
}

- (void)updateShowingPopupInfoView {
    if (self.popupInfoView.isShowing && self.popupInfoView.popupPricingId == 0) {
        [self updatePopupInfoViewPriceCell];
        [self.popupInfoView updatePricingToAlertView];
    }
}

- (void)realShowPopupInfoView {
    // 领劵成功，需要显示优惠弹窗
    // 如果弹窗优惠卷跟当前选中的价格包不一致，则刷新当前价格选中框
    NSInteger const popupPricingId = self.popupInfoView.popupPricingId;
    if (popupPricingId > 0) {
        IMYSubGuidePricingListItem *currentPricing = nil;
        for (IMYSubGuidePricingListItem *pricing in self.vipInfo.pricing_list) {
            if (popupPricingId == pricing.id) {
                currentPricing = pricing;
                break;
            }
        }
        if (self.currentPricing != currentPricing) {
            [self handleSelectedPricing:currentPricing isUserAction:NO];
        }
        // 获取当前价格包Cell，等框架动画结束后 再弹优惠券
        BOOL hasPriceCell = [self updatePopupInfoViewPriceCell];
        if (hasPriceCell) {
            [self.popupInfoView show];
        } else {
            @weakify(self);
            imy_asyncMainBlock(0.3, ^{
                @strongify(self);
                [self updatePopupInfoViewPriceCell];
                [self.popupInfoView show];
            });
        }
    } else {
        // 无指定价格包
        [self updatePopupInfoViewPriceCell];
        [self.popupInfoView show];
    }
}

- (BOOL)updatePopupInfoViewPriceCell {
    // 获取当前价格包Cell
    IMYSubGuideVipPriceCell *cell = nil;
    IMYSubGuidePricingListItem *targetPricing = nil;
    
    // 存在锚定，则直接使用当前价格包
    if (self.popupInfoView.popupPricingId > 0) {
        targetPricing = self.currentPricing;
    } else {
        // 无锚定，优先使用锚定的价格包，无才取第一个价格包
        NSArray<NSNumber *> * const popupValidPriceIds = self.popupInfoView.popupValidPriceIds;
        BOOL const containCurrentPricing = [popupValidPriceIds bk_any:^BOOL(NSNumber *pid) {
            return pid.integerValue == self.currentPricing.id;
        }];
        if (containCurrentPricing) {
            targetPricing = self.currentPricing;
        } else {
            NSInteger firstPricingId = popupValidPriceIds.firstObject.integerValue;
            for (IMYSubGuidePricingListItem *pricing in self.vipInfo.pricing_list) {
                if (firstPricingId == pricing.id) {
                    targetPricing = pricing;
                    break;
                }
            }
        }
    }
    
    // 获取目标价格包的Cell
    if (targetPricing != nil) {
        NSIndexPath *indexPath = [self indexOfPricing:targetPricing inPricings:self.vipInfo.pricing_list];
        if (indexPath && [self.priceCollectionView numberOfItemsInSection:indexPath.section] > indexPath.item) {
            cell = [self.priceCollectionView cellForItemAtIndexPath:indexPath];
        }
    }
    
    // 赋值给红包动画框架
    self.popupInfoView.currentPricing = targetPricing;
    self.popupInfoView.currentPriceCell = cell;
    
    // 获取价格列表视图位置
    UIView * const priceBoxView = self.priceCollectionView;
    self.popupInfoView.currentPriceBoxFrame = [priceBoxView convertRect:priceBoxView.bounds toView:nil];
    
    // 如果价格包View不可见，需要刷新并重新锚定
    return (cell != nil);
}

#pragma mark - Helper

/// [自动锚定价格] 索引
- (NSIndexPath *)indexOfPricing:(IMYSubGuidePricingListItem *)pricing
                     inPricings:(NSArray<IMYSubGuidePricingListItem *> *)pricings {
    if (!pricing || !pricings) {
        return nil;
    }
    
    __block NSInteger pricingIndex = -1;
    [pricings enumerateObjectsUsingBlock:^(IMYSubGuidePricingListItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (obj.id == pricing.id) {
            pricingIndex = idx;
            *stop = YES;
        }
    }];
    
    if (pricingIndex < 0 || pricingIndex >= pricings.count) {
        return nil;
    }
    
    return [NSIndexPath indexPathForRow:pricingIndex inSection:0];
}

#pragma mark - Button Actions

- (void)handleClickMaskEvent:(id)sender {
    [self dismiss];
}

- (void)handleCloseBtnEvent:(id)sender {
    [self dismiss];
}

- (void)handlePayBtnEvent:(id)sender {
    // [dy_qrxybzfan]：订阅_确认协议并支付按钮
    [self biReportWithAction:2 eventName:@"dy_qrxybzfan" pricing:self.currentPricing];
    
    [self doPayBtnAction];
}

- (void)biReportWithAction:(NSInteger const)action
                 eventName:(NSString * const)eventName
                   pricing:(IMYSubGuidePricingListItem * const)pricing {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    
    dict[@"event"] = eventName;
    dict[@"action"] = @(action);
    dict[@"subscribe_type"] = @([IMYRightsSDK sharedInstance].currentSubscribeType);
    dict[@"subscribe_gift"] = pricing.getAllGiftIDs;
    dict[@"subscribe_price"] = pricing.real_discount_price;
    dict[@"sub_price_id"] = @(pricing.id);
    dict[@"public_type"] = [IMYSubGuideManager biPublicTypeFromScnekey:self.session.sceneKey];
    dict[@"public_info"] = (IMYSubGuideManager.isFromVIPCenterTab ? @"是" : @"否");
    dict[@"info_key"] = @"通用";
    dict[@"public_key"] = @"通用";
    
    [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
}

- (void)doPayBtnAction {
    if (![IMYNetState networkEnable]) {
        [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
        return;
    }
    
    if (!self.checkPrivateView.checkSeleted) {
        IMYSubGuideProtocolReadView *readView = [IMYSubGuideProtocolReadView readViewWithSceneKey:self.session.sceneKey];
        readView.currentPricing = self.currentPricing;
        @weakify(self);
        [readView setAgreeBlock:^{
            @strongify(self);
            self.checkPrivateView.checkSeleted = YES;
            [self doPayBtnAction];
        }];
        [readView show];
        return;
    }
    
    self.session.currentPricing = self.currentPricing;
    self.session.priceId = self.currentPricing.id;
    self.session.sub_type = self.currentPricing.sub_type;
    
    // 普通赠礼id
    NSMutableArray *gift_ids = [NSMutableArray array];
    // 限时促销赠礼id
    NSMutableArray *gift_promotion_ids = [NSMutableArray array];
    // 遍历用户选中的赠礼
    [self.giftInfoView.selectedItems bk_each:^(IMYSubGuidePricingGiftInfoListItem *element) {
        if (element.user_promotion_id > 0 && element.hasCountdown) {
            [gift_promotion_ids addObject:@(element.user_promotion_id)];
        } else {
            [gift_ids addObject:@(element.id)];
        }
    }];
    self.session.gift_info = [gift_ids imy_jsonString];
    self.session.gift_promotion_ids = gift_promotion_ids;
    
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        [self doPayShouldLoginAction];
    } else {
        // 存在取消支付优惠弹窗
        if ([self.popupInfoView hasPopupsInfoWithTriggerScene:IMYSGRPopupsTriggerScenePaymentCancel]) {
            // 一定要强持有self，支付过程会把当前 dialog dismiss 掉
            // 这边是故意不用 weak 的
            self.session.onPayUserCancelBlock = ^(UIView<IMYAlertShowViewProtocol> * const payRetView) {
                UIWindow *rootView = self.window;
                rootView.userInteractionEnabled = NO;
                [self.popupInfoView payCancelledAction:^(BOOL needShow) {
                    rootView.userInteractionEnabled = YES;
                    if (!needShow) {
                        [payRetView show];
                    } else {
                        // 重新唤起，等动画结束后，进行弹窗
                        [self show];
                        imy_asyncMainBlock(0.3, ^{
                            [self realShowPopupInfoView];
                        });
                    }
                }];
            };
        }
        
        // 开始支付（隐藏支付半弹窗）
        [self dismiss];
        [[IMYSubGuideManager sharedInstance] payWithSession:self.session];
    }
}

- (void)doPayShouldLoginAction {
    // 防止登录页面多次回调
    __block BOOL isRunnning = NO;
    @weakify(self);
    void (^loginFinishedBlock)(UIViewController *) = ^(UIViewController *loginVC) {
        [loginVC dismissViewControllerAnimated:YES completion:^{
            @strongify(self);
            if (isRunnning) {
                return;
            }
            isRunnning = YES;
            // 判断当前页面是否被释放
            if (!self.currentShowVC || !self.currentShowVC.view.window) {
                // 页面被退出，取消支付半弹窗
                [self dismiss];
            } else {
                if (IMYRightsSDK.sharedInstance.currentRightsType != IMYRightsTypeNone) {
                    // 有会员，取消支付半弹窗
                    [self dismiss];
                    // 把业务页面全部回到首页
                    [self.currentShowVC.imy_navigationController popToRootViewControllerAnimated:NO];
                    // 先切到首页
                    IMYHIVE_BINDER(IOCAppMainTabVC).selectedTabIndexType = SYTabBarIndexTypeHome;
                    // 再切到会员tab
                    [[IMYURIManager sharedInstance] runActionWithPath:@"myrights/home" params:nil info:nil];
                } else {
                    // 无会员，继续支付流程
                    // 保留支付半弹窗
                }
            }
        }];
    };
    NSDictionary *loginMap = @{
        @"finishedBlock" : loginFinishedBlock,
    };
    // 未登录，唤起登录VC
    [[IMYURIManager sharedInstance] runActionWithPath:@"login" params:loginMap info:nil];
}

#pragma mark - UICollectionViewDelegate

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.vipInfo.pricing_list.count;
}

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return 1;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    IMYSubGuideVipPriceCell * const cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"IMYSubGuideVipPriceCell"
                                                                                     forIndexPath:indexPath];
    
    IMYSubGuidePricingListItem *pricing = self.vipInfo.pricing_list[indexPath.item];
    cell.isBigSize = (self.priceCardStyle == 1 ? YES : NO);
    [cell setPricing:pricing currentPricing:self.currentPricing];
    
    // [dy_hyjgkp] 订阅_会员价格卡片
    cell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"dy_hyjgkp_dialog-%p-%ld", self, indexPath.row];
    // 精准曝光控制
    if (cell.isBigSize) {
        // 大卡曝光比例 78%，无间隙
        cell.imyut_eventInfo.showRadius = 0.78;
        cell.imyut_eventInfo.edgeOffset = UIEdgeInsetsZero;
    } else {
        // 小卡曝光比例 84%，左右有12pt的间隙
        cell.imyut_eventInfo.showRadius = 0.84;
        cell.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(0, 12, 0, 12);
    }
    @weakify(self);
    [cell.imyut_eventInfo setShouldExposureDetectingBlock:^BOOL(__kindof UIView *view) {
        @strongify(self);
        return self.isCanExpos;
    }];
    [cell.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        [self biReportWithAction:1 eventName:@"dy_hyjgkp" pricing:pricing];
    }];
    
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    IMYSubGuidePricingListItem *currentPricing = self.vipInfo.pricing_list[indexPath.item];
    [self handleSelectedPricing:currentPricing isUserAction:YES];
    
    // [dy_hyjgkp] 订阅_会员价格卡片
    [self biReportWithAction:2 eventName:@"dy_hyjgkp" pricing:currentPricing];
}

- (void)handleSelectedPricing:(IMYSubGuidePricingListItem *)currentPricing isUserAction:(BOOL)isUserAction {
    self.currentPricing = currentPricing;
    self.session.currentPricing = currentPricing;
    self.session.priceId = currentPricing.id;
    self.session.sub_type = currentPricing.sub_type;
    self.lastSelectedPricingType = currentPricing.sub_type;
    
    [self.priceCollectionView reloadData];
    
    NSIndexPath *indexPath = [self indexOfPricing:self.currentPricing inPricings:self.vipInfo.pricing_list];
    if (indexPath && [self.priceCollectionView numberOfItemsInSection:indexPath.section] > indexPath.item) {
        [self.priceCollectionView scrollToItemAtIndexPath:indexPath
                                         atScrollPosition:UICollectionViewScrollPositionCenteredHorizontally
                                                 animated:isUserAction ? YES : NO];
    }
    
    // 快速领劵
    [self loadPopupsInfoWithRefresh:YES];
    
    @weakify(self);
    imy_asyncMainBlock(^{
        @strongify(self);
        // 内容更新
        [self refreshAllViews];
        // 刷新支付按钮文案
        [self refreshPayBtnShowText:nil];
    });
    
    // 修正角标位置
    imy_asyncMainBlock(^{
        @strongify(self);
        [self refreshCurrentPricingOffsetX];
    });
    
}


- (void)refreshCurrentPricingOffsetX {
    if (!self.currentPricing) {
        return;
    }
    const NSUInteger index = [self.vipInfo.pricing_list indexOfObject:self.currentPricing];
    if (index == NSNotFound) {
        return;
    }
    if (self.giftInfoView.imy_height < 1) {
        // 无赠礼
        return;
    }
    NSIndexPath *indexPath = [NSIndexPath indexPathForItem:index inSection:0];
    UICollectionViewLayoutAttributes *layout = [self.priceCollectionView layoutAttributesForItemAtIndexPath:indexPath];
    if (!layout) {
        // 还未布局完成, 0.1秒再次执行
        imy_asyncMainBlock(0.1, ^{
            [self refreshCurrentPricingOffsetX];
        });
        return;
    }
    // 赠礼View有12的偏移量
    self.currentPricingOffsetX = layout.center.x - 12;
    // 修正角标位置
    [self scrollViewDidScroll:self.priceCollectionView];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (scrollView != self.priceCollectionView) {
        return;
    }
    // 更新Arrow角标位置
    if (self.giftInfoView.imy_height > 1) {
        CGFloat arrowX = self.currentPricingOffsetX - scrollView.contentOffset.x;
        [self.giftInfoView updateWithArrowX:arrowX];
    }
}

#pragma mark - Set

- (void)resetPayBtnShowTextWithAnimationEnd {
    @weakify(self);
    // 兜底：价格动画结束后，显示原始文案，1秒限流器
    imy_throttle(1, ^{
        @strongify(self);
        [self refreshPayBtnShowText:nil];
    });
}

/// 刷新支付按钮文案
- (void)refreshPayBtnShowText:(NSString *)showPriceText {
    // ¥x 立即开通
    NSString *suffix = self.currentPricing.sub_btn_text;
    if (imy_isEmptyString(suffix)) {
        suffix = self.vipInfo.sub_btn_title;
    }
    if (!showPriceText) {
        showPriceText = self.currentPricing.real_discount_price;
    }
    
    // 优惠金额 = 原价 - 当前折扣价 + 赠礼价值
    __block CGFloat allWorth = MAX(0, [self.currentPricing.price doubleValue] - [self.currentPricing.real_discount_price doubleValue]);
    if (self.giftInfoView.imy_height > 1) {
        // 有显示赠礼的时候，才累加赠礼价值
        [self.giftInfoView.selectedItems enumerateObjectsUsingBlock:^(IMYSubGuidePricingGiftInfoListItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            allWorth += obj.worth.doubleValue;
        }];
    }
    
    NSString *allWorthText = [NSString imy_getPriceNoneString:allWorth];
    
    NSString *btnTitle = [NSString stringWithFormat:@"¥%@%@", showPriceText, suffix];
    btnTitle = [btnTitle stringByReplacingOccurrencesOfString:@"{y}" withString:allWorthText];
    [self.payBtn setTitle:btnTitle forState:UIControlStateNormal];
    
    // 对括号内的字体变小
    NSCharacterSet *beginCharSet = [NSCharacterSet characterSetWithCharactersInString:@"(（"];
    NSCharacterSet *endCharSet = [NSCharacterSet characterSetWithCharactersInString:@")）"];
    
    NSRange regularBegin = [btnTitle rangeOfCharacterFromSet:beginCharSet];
    NSRange regularEnd = [btnTitle rangeOfCharacterFromSet:endCharSet options:NSBackwardsSearch];
    
    if (regularBegin.location != NSNotFound &&
        regularEnd.location != NSNotFound &&
        regularEnd.location > regularBegin.location) {
        // 设置括号内的小字体
        NSMutableAttributedString *atts = [[NSMutableAttributedString alloc] initWithString:btnTitle];
        [atts addAttributes:@{
            NSFontAttributeName : [UIFont systemFontOfSize:13 weight:UIFontWeightRegular],
        } range:NSMakeRange(regularBegin.location, regularEnd.location - regularBegin.location + 1)];
        
        self.payBtn.titleLabel.attributedText = atts;
        self.payBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
    }
}

#pragma mark - 竖直返回手势处理

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    UITableView *tableView = self.recommendView.tableView;
    if (otherGestureRecognizer == tableView.panGestureRecognizer) {
        return YES;
    }
    return NO;
}

- (BOOL)gestureRecognizerShouldBegin:(UIPanGestureRecognizer *)gestureRecognizer {
    CGPoint velocity = [gestureRecognizer velocityInView:gestureRecognizer.view];
    if (fabs(velocity.y) < fabs(velocity.x)) {
        // 返回手势只考虑纵向手势
        return NO;
    }
    return YES;
}

- (void)handlePanGesture:(UIPanGestureRecognizer *)panGesture {
    switch (panGesture.state) {
        case UIGestureRecognizerStateBegan: {
            self.panVelocityY = 0;
        } break;
        case UIGestureRecognizerStateChanged: {
            UITableView *tableView = self.recommendView.tableView;
            // 同时触发 tableView 滚动手势
            if (tableView.panGestureRecognizer.state != UIGestureRecognizerStatePossible) {
                if (tableView.contentOffset.y < 0) {
                    // tableView 继续下拉，则改为卡片下拉，并取消tableView滑动手势响应
                    tableView.contentOffset = CGPointZero;
                } else if (self.cardView.imy_top > self.panMinY) {
                    // tablview 下拉后，在继续上拉
                    tableView.contentOffset = CGPointZero;
                } else {
                    // 其他情况都是，只响应滑动tableView，不处理卡片的手势操作
                    [panGesture setTranslation:CGPointZero inView:panGesture.view];
                    return;
                }
            }
            
            // 获取偏移距离
            CGPoint translation = [panGesture translationInView:panGesture.view];
            CGPoint velocity = [panGesture velocityInView:panGesture.view];
            [panGesture setTranslation:CGPointZero inView:panGesture.view];
            CGFloat y = self.cardView.imy_top + translation.y;
            if (y < self.panMinY) {
                y = self.panMinY;
            } else if (y > self.panMaxY) {
                y = self.panMaxY;
            } else {
                if (velocity.y != 0) {
                    self.panVelocityY = velocity.y;
                }
            }
            self.cardView.imy_top = y;
        } break;
        case UIGestureRecognizerStateEnded:
        case UIGestureRecognizerStateCancelled:
        case UIGestureRecognizerStateFailed: {
            self.cardView.userInteractionEnabled = NO;
            [UIView animateWithDuration:0.15 animations:^{
                BOOL toPanMinY = NO;
                if (fabs(self.panVelocityY) > 1) {
                    // 如果有滑动速度 则用滑动速度的方向 作为最终方向
                    toPanMinY = (self.panVelocityY < 0);
                } else {
                    // 否则判断距离哪边近 作为最终方向
                    toPanMinY = (self.panMaxY - self.cardView.imy_top) >= (self.cardView.imy_top - self.panMinY);
                }
                if (toPanMinY) {
                    self.cardView.imy_top = self.panMinY;
                    self.isPanToUp = YES;
                } else {
                    self.cardView.imy_top = self.panMaxY;
                    self.isPanToUp = NO;
                }
            } completion:^(BOOL finished) {
                self.cardView.userInteractionEnabled = YES;
            }];
            self.panVelocityY = 0;
        }
        default: {
            break;
        }
    }
}

#pragma mark - Get

- (UIView *)maskControl {
    if (!_maskControl) {
        IMYTouchEXView *touchView = [IMYTouchEXView new];
        @weakify(self);
        touchView.onTouchUpInsideBlock = ^{
            @strongify(self);
            [self handleClickMaskEvent:nil];
        };
        _maskControl = touchView;
    }
    return _maskControl;
}

- (UIView *)cardView {
    if (!_cardView) {
        _cardView = [UIView new];
        [_cardView imy_drawTopCornerRadius:12];
        [_cardView imy_setBackgroundColor:kCK_White_ANQ];
        
        if (self.priceCardStyle == 1) {
            _cardWhiteLayer = [CALayer layer];
            _cardWhiteLayer.backgroundColor = UIColor.whiteColor.CGColor;
            _cardWhiteLayer.frame = CGRectMake(0, 0, SCREEN_WIDTH, 187);
            [_cardView.layer addSublayer:_cardWhiteLayer];
            
            _cardGradientLayer = [CAGradientLayer layer];
            _cardGradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#FFFFFF").CGColor,
                                          (__bridge id)[IMY_COLOR_KEY(@"#FFFFFF") colorWithAlphaComponent:0].CGColor];
            _cardGradientLayer.locations = @[@0.0, @1.0];
            _cardGradientLayer.startPoint = CGPointMake(0, 0);
            _cardGradientLayer.endPoint = CGPointMake(0, 1);
            _cardGradientLayer.frame = CGRectMake(0, _cardWhiteLayer.frame.size.height, SCREEN_WIDTH, 105);
            [_cardView.layer addSublayer:_cardGradientLayer];
            
            // 夜间模式
            @weakify(self);
            [_cardView imy_addThemeChangedBlock:^(id weakObject) {
                @strongify(self);
                if (IMYPublicAppHelper.shareAppHelper.isNight) {
                    self.cardWhiteLayer.hidden = YES;
                    self.cardGradientLayer.hidden = YES;
                } else {
                    self.cardWhiteLayer.hidden = NO;
                    self.cardGradientLayer.hidden = NO;
                }
            }];
        }
    }
    return _cardView;
}

- (IMYTouchEXButton *)closeBtn {
    if (!_closeBtn) {
        _closeBtn = [[IMYTouchEXButton alloc] initWithFrame:CGRectZero];
        [_closeBtn imy_addThemeChangedBlock:^(IMYTouchEXButton *weakObject) {
            UIImage *image = [UIImage imy_imageForKey:@"dyzf_icon_close"];
            [weakObject imy_setImage:image.imy_getNightStyleBottomBarImage];
        }];
        [_closeBtn addTarget:self action:@selector(handleCloseBtnEvent:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeBtn;
}

- (UICollectionView *)priceCollectionView {
    if (!_priceCollectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.minimumInteritemSpacing = 8.0;
        layout.minimumLineSpacing = 8.0;
        layout.itemSize = [IMYSubGuideVipPriceCell showItemSizeWithStyle:self.priceCardStyle];
        layout.sectionInset = UIEdgeInsetsMake(0, 12, 0, 12);
        layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        
        CGFloat const boxWidth = (self.priceCardStyle == 1 ? SCREEN_WIDTH : SCREEN_WIDTH - 24);
        _priceCollectionView = [[UICollectionView alloc] initWithFrame:CGRectMake(0, 0, boxWidth, layout.itemSize.height)
                                                  collectionViewLayout:layout];
        _priceCollectionView.backgroundColor = [UIColor clearColor];
        _priceCollectionView.clipsToBounds = NO;
        _priceCollectionView.delegate = self;
        _priceCollectionView.dataSource = self;
        [_priceCollectionView registerClass:IMYSubGuideVipPriceCell.class forCellWithReuseIdentifier:@"IMYSubGuideVipPriceCell"];
        _priceCollectionView.showsHorizontalScrollIndicator = NO;
    }
    return _priceCollectionView;
}

- (IMYSubGuideVipGiftInfoView *)giftInfoView {
    if (!_giftInfoView) {
        CGFloat const boxWidth = (self.priceCardStyle == 1 ? SCREEN_WIDTH - 24 : SCREEN_WIDTH - 48);
        _giftInfoView = [IMYSubGuideVipGiftInfoView giftInfoViewWithBoxWidth:boxWidth];
        _giftInfoView.showStyle = self.priceCardStyle;
        _giftInfoView.arrowItemWidth = [IMYSubGuideVipPriceCell showItemSizeWithStyle:self.priceCardStyle].width;
        _giftInfoView.sceneKey = self.session.sceneKey;
        @weakify(self);
        _giftInfoView.onSelectItemDidChanged = ^(void) {
            @strongify(self);
            [self refreshPayBtnShowText:nil];
        };
    }
    return _giftInfoView;
}

- (UILabel *)descLabel {
    if (!_descLabel) {
        _descLabel = [[UILabel alloc] init];
        _descLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        _descLabel.textColor = IMY_COLOR_KEY(kCK_Black_B);
        _descLabel.numberOfLines = 0;
    }
    return _descLabel;
}

- (IMYCapsuleButton *)payBtn {
    if (!_payBtn) {
        CGFloat const boxWidth = (self.priceCardStyle == 1 ? SCREEN_WIDTH - 24 : SCREEN_WIDTH - 48);
        _payBtn = [[IMYCapsuleButton alloc] initWithFrame:CGRectMake(0, 0, boxWidth, 48)];
        _payBtn.type = IMYButtonTypeFillRed;
        _payBtn.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        [_payBtn addTarget:self action:@selector(handlePayBtnEvent:) forControlEvents:UIControlEventTouchUpInside];
        
        [_payBtn setTitle:IMYString(@"确认协议并支付") forState:UIControlStateNormal];
        [_payBtn imy_drawAllCornerRadius:24];
        
        _payBtn.titleAtDirection = IMYDirectionCenterX | IMYDirectionCenterY;
        
        // 渐变背景色
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#C34DFF").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FF4D6A").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FFA64D").CGColor];
        gradientLayer.locations = @[@0.0, @0.5, @1.0];
        gradientLayer.startPoint = CGPointMake(0, 0);
        gradientLayer.endPoint = CGPointMake(1, 0);
        gradientLayer.frame = _payBtn.bounds;
        [_payBtn.layer insertSublayer:gradientLayer atIndex:0];
    }
    return _payBtn;
}

- (IMYSubGuideCheckPrivateView *)checkPrivateView {
    if (!_checkPrivateView) {
        _checkPrivateView = [[IMYSubGuideCheckPrivateView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 24, 0)];
        _checkPrivateView.checkSeleted = NO;
        _checkPrivateView.isAlignmentCenter = (self.priceCardStyle == 1 ? YES : NO);
    }
    return _checkPrivateView;
}

@end
