//
//  IMYSubGuideVipInfo.m
//  demo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/31.
//

#import "IMYSubGuideVipInfo.h"
#import "IMYBaseKit.h"
#import "IMYSubGuideMacro.h"
#import "NSAttributedString+IMYRights.h"

@interface IMYSubGuidePricingListItem () <IMYTimerRuningProtocol>

@end

@implementation IMYSubGuidePricingListItem
@synthesize getAllGiftIDs = _allGiftIDs;

- (NSString *)getAllGiftIDs {
    if (!_allGiftIDs && _gift_info.gift_list.count > 0) {
        NSMutableString *mutaStr = [NSMutableString string];
        for (IMYSubGuidePricingGiftInfoListItem *item in _gift_info.gift_list) {
            if (mutaStr.length > 0) {
                [mutaStr appendString:@","];
            }
            [mutaStr appendFormat:@"%ld", item.id];
        }
        _allGiftIDs = [NSString stringWithFormat:@"[%@]", mutaStr];
    }
    return _allGiftIDs;
}

- (NSString *)real_discount_price {
    if (_countdownTime > 0 && _promotion_info.promotion_amount.length > 0) {
        return _promotion_info.promotion_amount;
    } else {
        return _discount_price;
    }
}

- (void)setPromotion_info:(IMYSubGuidePricingGiftInfoModel *)promotion_info {
    if (IMYRightsSDK.isSubAuditReview) {
        return;
    }
    _promotion_info = promotion_info;
    if (_promotion_info.count_down_seconds > 0) {
        [self setupCountdownTime:_promotion_info.count_down_seconds];
    } else {
        [self setupCountdownTime:0];
    }
}

- (void)setupCountdownTime:(NSInteger const)countdownTime {
    _countdownTime = MAX(0, countdownTime);
    if (_countdownTime > 0) {
        [[IMYTimerHelper defaultTimerHelper] addTimerForObject:self];
    } else {
        [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];
    }
}

- (void)imy_timerRuning {
    _countdownTime -= 1;
    if (_countdownTime <= 0) {
        _countdownTime = 0;
        [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];
        // 发送优惠价结束通知
        [[NSNotificationCenter defaultCenter] postNotificationName:KRightsSubGuidePromotionChangedNotification
                                                            object:self];
    }
}

- (NSAttributedString *)getAttributedStringByDesc {
    return [NSAttributedString imyrs_getNormalStringWithText:self.desc];
}

@end

@implementation IMYSubGuidePricingPromotionInfoModel

@end

@implementation IMYSubGuidePricingGiftInfoModel

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"gift_list": [IMYSubGuidePricingGiftInfoListItem class]
    };
}

@end

@implementation IMYSubGuidePricingBtnTagModel

@end

@implementation IMYSubGuidePricingGiftInfoListItem

- (void)setCountdown_seconds:(NSInteger)countdown_seconds {
    _countdown_seconds = MAX(0, countdown_seconds);
    if (_countdown_seconds > 0) {
        _hasCountdown = YES;
        [[IMYTimerHelper defaultTimerHelper] addTimerForObject:self];
    } else {
        _hasCountdown = NO;
        [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];
    }
}

- (void)imy_timerRuning {
    _countdown_seconds -= 1;
    if (_countdown_seconds <= 0) {
        _countdown_seconds = 0;
        [[IMYTimerHelper defaultTimerHelper] removeTimerForObject:self];
        // 发送优惠价结束通知
        [[NSNotificationCenter defaultCenter] postNotificationName:KRightsSubGuidePromotionChangedNotification
                                                            object:nil];
    }
}

@end

@implementation IMYSubGuideRightsInfoItem

@end

@implementation IMYSubGuideRightsListItem

@end

@implementation IMYSubGuideImageModel

@end

@implementation IMYSubGuideRightsGroupListItem

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"images": [IMYSubGuideImageModel class]
    };
}

@end

@implementation IMYSubGuideBackPopupModel

@end

@implementation IMYSubGuideVipInfo

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"pricing_list": [IMYSubGuidePricingListItem class],
        @"rights_group_list": [IMYSubGuideRightsGroupListItem class],
        @"dialog_group_list": [IMYSubGuideRightsGroupListItem class],
    };
}

@end
