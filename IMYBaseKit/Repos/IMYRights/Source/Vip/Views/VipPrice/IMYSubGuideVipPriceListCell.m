//
//  IMYSubGuideVipPriceListCell.m
//  demo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/2.
//

#import "IMYSubGuideVipPriceListCell.h"
#import "IMYBaseKit.h"
#import "IMYSubGuideManager.h"
#import "IMYSubGuideVipPriceCell.h"
#import "IMYSubGuideCheckPrivateView.h"
#import "IMYSubGuideProtocolReadView.h"
#import "IMYSubGuideRPAnimationView.h"
#import "IMYSubGuideVipGiftInfoView.h"

@interface IMYSubGuideVipPriceListCell () <UICollectionViewDelegate, UICollectionViewDataSource>

@property (nonatomic, strong) IMYSubGuideVipInfoCellModel *cellModel;
@property (nonatomic, copy) NSString *sceneKey;
@property (nonatomic, assign) BOOL isCanExpos; ///< 是否可以开启曝光检测（默认 NO，首次锚定结束延迟 0.1 秒 YES）
@property (nonatomic, assign) IMYSubGuideLimitType limitType;

@property (nonatomic, strong) IMYSubGuidePricingListItem *currentPricing;

/// 选中价格的偏移坐标
@property (nonatomic, assign) CGFloat currentPricingOffsetX;

@property (nonatomic, strong) UIView *cardView;
@property (nonatomic, strong) CALayer *cardWhiteLayer;
@property (nonatomic, strong) CAGradientLayer *cardGradientLayer;

@property (nonatomic, strong) UICollectionView *priceCollectionView;
@property (nonatomic, strong) IMYSubGuideVipGiftInfoView *giftInfoView;
@property (nonatomic, strong) UILabel *descLabel;
@property (nonatomic, strong) IMYCapsuleButton *payBtn;
@property (nonatomic, strong) IMYSubGuideCheckPrivateView *checkPrivateView;

/// 卡片样式，0：标准版，1：大卡片
@property (nonatomic, assign, readonly) NSInteger priceCardStyle;

@end

@implementation IMYSubGuideVipPriceListCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.imy_size = CGSizeMake(SCREEN_WIDTH, 240);
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = [UIColor clearColor];
        self.contentView.backgroundColor = [UIColor clearColor];
        
        // 价格卡片样式
        IMYABTestExperiment *exp = [[IMYABTestManager sharedInstance] experimentForKey:@"PriceIsBigCard"];
        if ([exp.vars integerForKey:@"PriceCard"] == 1) {
            _priceCardStyle = 1;
        } else {
            _priceCardStyle = 0;
        }
        
        [self.contentView addSubview:self.cardView];
        [self.cardView addSubview:self.priceCollectionView];
        [self.cardView addSubview:self.giftInfoView];
        [self.cardView addSubview:self.descLabel];
        [self.cardView addSubview:self.payBtn];
        [self.cardView addSubview:self.checkPrivateView];
        
        [self.cardView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.contentView.mas_top).offset(0);
            make.leading.mas_equalTo(self.contentView.mas_leading).offset(self.priceCardStyle == 1 ? 0 : 12);
            make.trailing.mas_equalTo(self.contentView.mas_trailing).offset(self.priceCardStyle == 1 ? 0 :-12);
            make.height.mas_equalTo([self cardHeight]);
        }];
        
        [self.priceCollectionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.cardView.mas_top).offset(self.priceCardStyle == 1 ? 24 : 18);
            make.leading.mas_equalTo(self.cardView.mas_leading).offset(0);
            make.trailing.mas_equalTo(self.cardView.mas_trailing).offset(0);
            make.height.mas_equalTo([IMYSubGuideVipPriceCell showItemSizeWithStyle:self.priceCardStyle].height);
        }];
        
        self.giftInfoView.alpha = 0;
        [self.giftInfoView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.priceCollectionView.mas_bottom).offset(8);
            make.leading.mas_equalTo(self.cardView.mas_leading).offset(12);
            make.trailing.mas_equalTo(self.cardView.mas_trailing).offset(-12);
            make.height.mas_equalTo(0);
        }];
        
        self.descLabel.alpha = 0;
        [self.descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.giftInfoView.mas_bottom).offset(-8);
            make.leading.mas_equalTo(self.cardView.mas_leading).offset(12);
            make.trailing.mas_equalTo(self.cardView.mas_trailing).offset(-12);
            make.height.mas_equalTo(0);
        }];
        
        [self.payBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.descLabel.mas_bottom).offset(8);
            make.leading.mas_equalTo(self.cardView.mas_leading).offset(12);
            make.trailing.mas_equalTo(self.cardView.mas_trailing).offset(-12);
            make.height.mas_equalTo(48);
        }];
        
        CGSize checkPrivateSize = self.checkPrivateView.imy_size;
        [self.checkPrivateView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.payBtn.mas_bottom).offset(8);
            make.leading.mas_equalTo(self.cardView.mas_leading).offset(12);
            make.trailing.mas_equalTo(self.cardView.mas_trailing).offset(-12);
            make.height.mas_equalTo(checkPrivateSize.height);
        }];
        
        // 直接完成布局
        [self layoutIfNeeded];
    }
    return self;
}

#pragma mark - 锚定价格

/// 选中价格
/// - Parameters:
///   - pricing: 价格
///   - needCheck: 是否需要自动锚定
- (void)selectPricing:(IMYSubGuidePricingListItem *)pricing isCheck:(BOOL)isCheck needScroll:(BOOL)needScroll {
    _currentPricing = pricing;
    
    // 赠礼
    self.giftInfoView.sceneKey = self.sceneKey;
    [self.giftInfoView updateWithCurrentPricing:self.currentPricing];
    [self.giftInfoView updateWithArrowX:self.currentPricingOffsetX];
    [self.giftInfoView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(self.giftInfoView.imy_height);
    }];
    
    // desc
    {
        NSAttributedString * const descAttr = [self.currentPricing getAttributedStringByDesc];
        self.descLabel.alpha = (descAttr.length > 0 ? 1 : 0);
        [self.descLabel imy_addThemeActionBlock:^(UILabel *weakObject) {
            weakObject.textColor = IMY_COLOR_KEY(kCK_Black_B);
            weakObject.attributedText = descAttr;
        } forKey:@"setTextColor"];
        CGFloat offsetY = 8;
        CGFloat height = 16;
        if (self.giftInfoView.alpha < 0.01) {
            offsetY -= 8;
        }
        if (self.descLabel.alpha < 0.01) {
            offsetY -= 8;
            height = 0;
        } else {
            CGFloat const boxWidth = (self.priceCardStyle == 1 ? SCREEN_WIDTH - 24 : SCREEN_WIDTH - 48);
            self.descLabel.imy_width = boxWidth;
            [self.descLabel imy_sizeToFitHeight];
            height = MAX(16, self.descLabel.imy_height);
        }
        self.descLabel.imy_top = self.giftInfoView.imy_bottom + offsetY;
        self.descLabel.imy_height = height;
        [self.descLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.giftInfoView.mas_bottom).offset(offsetY);
            make.height.mas_equalTo(height);
        }];
    }
    
    // 按钮位置
    self.payBtn.imy_top = self.descLabel.imy_bottom + 8;
    
    // 协议刷新
    [self.checkPrivateView refreshWithType:self.currentPricing.type];
    CGSize checkPrivateSize = self.checkPrivateView.imy_size;
    [self.checkPrivateView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(checkPrivateSize.height);
    }];
    self.checkPrivateView.imy_top = self.payBtn.imy_bottom + 8;
    
    // 卡片高度更新
    CGFloat cardHeight = [self cardHeight];
    [self.cardView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(cardHeight);
    }];
    self.cardView.imy_height = cardHeight;
    
    // 渐变区颜色
    CGFloat const cardWhiteHeight = self.payBtn.imy_top - 8;
    self.cardWhiteLayer.frame = CGRectMake(0, 0, self.cardView.imy_width, cardWhiteHeight);
    self.cardGradientLayer.frame = CGRectMake(0, cardWhiteHeight, self.cardView.imy_width, self.cardView.imy_height - cardWhiteHeight);
    
    // ¥x 立即开通
    [self refreshPayBtnShowText];
    
    if (self.onGiftItemsSelectedBlock) {
        self.onGiftItemsSelectedBlock(self.giftInfoView.selectedItems);
    }
    
    // 价格锚定
    if (!needScroll) {
        return;
    }
    @weakify(self);
    imy_asyncMainBlock(^{
        @strongify(self);
        // 如果是自动锚定：未自动锚定过 1、 滚动 2、0.1 秒后放开曝光
        if (isCheck) {
            if (!self.hasChecked) {
                // 滚动
                NSLog(@"[自动锚定价格] 锚定");
                NSIndexPath *indexPath = [self indexOfPricing:pricing inPricings:self.cellModel.vipInfo.pricing_list];
                if (indexPath && [self.priceCollectionView numberOfItemsInSection:indexPath.section] > indexPath.item) {
                    [self.priceCollectionView scrollToItemAtIndexPath:indexPath
                                                     atScrollPosition:UICollectionViewScrollPositionCenteredHorizontally
                                                             animated:NO];
                }
                self.hasChecked = YES;
                if (!self.isCanExpos) {
                    imy_asyncMainBlock(0.1, ^{
                        @strongify(self);
                        self.isCanExpos = YES;
                        // 红包动画逻辑
                        [self handleRpAnimation];
                    });
                }
            }
        } else {
            // 普通锚定：滚动
            NSIndexPath *indexPath = [self indexOfPricing:pricing inPricings:self.cellModel.vipInfo.pricing_list];
            if (indexPath && [self.priceCollectionView numberOfItemsInSection:indexPath.section] > indexPath.item) {
                [self.priceCollectionView scrollToItemAtIndexPath:indexPath
                                                 atScrollPosition:UICollectionViewScrollPositionCenteredHorizontally
                                                         animated:YES];
            }
        }
    });
}

- (void)refreshPayBtnShowText {
    // ¥x 立即开通
    NSString *suffix = self.currentPricing.sub_btn_text;
    if (imy_isEmptyString(suffix)) {
        suffix = self.cellModel.vipInfo.sub_btn_title;
    }
    
    // 优惠金额 = 原价 - 当前折扣价 + 赠礼价值
    __block CGFloat allWorth = MAX(0, [self.currentPricing.price doubleValue] - [self.currentPricing.real_discount_price doubleValue]);
    [self.giftInfoView.selectedItems enumerateObjectsUsingBlock:^(IMYSubGuidePricingGiftInfoListItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        allWorth += obj.worth.doubleValue;
    }];
    NSString *allWorthText = [NSString imy_getPriceNoneString:allWorth];
    
    NSString *btnTitle = [NSString stringWithFormat:@"¥%@%@", self.currentPricing.real_discount_price, suffix];
    btnTitle = [btnTitle stringByReplacingOccurrencesOfString:@"{y}" withString:allWorthText];
    [self.payBtn setTitle:btnTitle forState:UIControlStateNormal];
    
    // 对括号内的字体变小
    NSCharacterSet *beginCharSet = [NSCharacterSet characterSetWithCharactersInString:@"(（"];
    NSCharacterSet *endCharSet = [NSCharacterSet characterSetWithCharactersInString:@")）"];
    
    NSRange regularBegin = [btnTitle rangeOfCharacterFromSet:beginCharSet];
    NSRange regularEnd = [btnTitle rangeOfCharacterFromSet:endCharSet options:NSBackwardsSearch];
    
    if (regularBegin.location != NSNotFound &&
        regularEnd.location != NSNotFound &&
        regularEnd.location > regularBegin.location) {
        // 设置括号内的小字体
        NSMutableAttributedString *atts = [[NSMutableAttributedString alloc] initWithString:btnTitle];
        [atts addAttributes:@{
            NSFontAttributeName : [UIFont systemFontOfSize:13 weight:UIFontWeightRegular],
        } range:NSMakeRange(regularBegin.location, regularEnd.location - regularBegin.location + 1)];
        
        self.payBtn.titleLabel.attributedText = atts;
        self.payBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
    }
}


/// [自动锚定价格] 索引
- (NSIndexPath *)indexOfPricing:(IMYSubGuidePricingListItem *)pricing
                     inPricings:(NSArray<IMYSubGuidePricingListItem *> *)pricings {
    if (!pricing || !pricings) {
        return nil;
    }
    
    __block NSInteger pricingIndex = -1;
    [pricings enumerateObjectsUsingBlock:^(IMYSubGuidePricingListItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (obj.id == pricing.id) {
            pricingIndex = idx;
            *stop = YES;
        }
    }];
    
    if (pricingIndex < 0 || pricingIndex >= pricings.count) {
        return nil;
    }
    
    return [NSIndexPath indexPathForRow:pricingIndex inSection:0];
}

#pragma mark - 动画逻辑

- (void)handleRpAnimation {
    if (!self.canShowRpAnimation || !self.isCanExpos) {
        return;
    }
    if (self.currentPricing.countdownTime > 0) {
        // 已有优惠倒计时，不执行红包动画
        return;
    }
    self.limitType = [IMYSubGuideManager isLimitWithPricing:self.currentPricing];
    if ([self isNeedAnimation]) {
        // 通知动画开始
        [[NSNotificationCenter defaultCenter] postNotificationName:K_Noti_SubGuid_RedPacket_Animation_Start
                                                            object:nil
                                                          userInfo:@{
            @"priceId": @(self.currentPricing.id),
            @"animType" : @1,
        }];
        
        if (self.limitType == IMYSubGuideLimitType_pass) {
            [IMYSubGuideManager addLimitCountWithPricing:self.currentPricing];
        } else if (self.limitType == IMYSubGuideLimitType_pass_reset) {
            [IMYSubGuideManager resetLimitCountWithPricing:self.currentPricing];
        }
    }
}

- (BOOL)isNeedAnimation {
    if (self.limitType == IMYSubGuideLimitType_pass ||
        self.limitType == IMYSubGuideLimitType_pass_reset ||
        self.limitType == IMYSubGuideLimitType_Pass_ignore) {
        return YES;
    }
    return NO;
}


#pragma mark - Button Actions

- (void)handlePayBtnEvent:(id)sender {
    if (self.clickPayBlock) {
        self.clickPayBlock();
    }
}

#pragma mark - UICollectionViewDelegate

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.cellModel.vipInfo.pricing_list.count;
}

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return 1;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    // 获取价格包信息
    IMYSubGuidePricingListItem * const pricing = self.cellModel.vipInfo.pricing_list[indexPath.item];
    // 价格包Cell
    IMYSubGuideVipPriceCell * const cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"IMYSubGuideVipPriceCell"
                                                                                     forIndexPath:indexPath];
    cell.isBigSize = (self.priceCardStyle == 1);
    [cell setPricing:pricing currentPricing:self.currentPricing];
    
    // [dy_hyjgkp] 订阅_会员价格卡片
    cell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"dy_hyjgkp-%p-%ld", self, indexPath.row];
    // 精准曝光控制
    if (cell.isBigSize) {
        // 大卡曝光比例 78%，无间隙
        cell.imyut_eventInfo.showRadius = 0.78;
        cell.imyut_eventInfo.edgeOffset = UIEdgeInsetsZero;
    } else {
        // 小卡曝光比例 84%，左右有12pt的间隙
        cell.imyut_eventInfo.showRadius = 0.84;
        cell.imyut_eventInfo.edgeOffset = UIEdgeInsetsMake(0, 12, 0, 12);
    }
    @weakify(self);
    [cell.imyut_eventInfo setShouldExposureDetectingBlock:^BOOL(__kindof UIView *view) {
        @strongify(self);
        return self.isCanExpos;
    }];
    [cell.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        [self biReportWithAction:1 eventName:@"dy_hyjgkp" pricing:pricing];
    }];
    
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    IMYSubGuidePricingListItem *pricing = self.cellModel.vipInfo.pricing_list[indexPath.item];
    self.currentPricing = pricing;
    
    [UIView performWithoutAnimation:^{
        [self.priceCollectionView reloadData];
    }];
    
    // 加个动画效果
    [UIView animateWithDuration:0.3 animations:^{
        [self selectPricing:self.currentPricing isCheck:NO needScroll:YES];
    }];
    
    // 修正角标位置
    imy_asyncMainBlock(^{
        [self refreshCurrentPricingOffsetX];
    });
    
    if (self.clickPriceBlock) {
        self.clickPriceBlock(self.currentPricing);
    }
    
    // [dy_hyjgkp] 订阅_会员价格卡片
    [self biReportWithAction:2 eventName:@"dy_hyjgkp" pricing:pricing];
}

- (void)biReportWithAction:(NSInteger const)action
                 eventName:(NSString * const)eventName
                   pricing:(IMYSubGuidePricingListItem * const)pricing {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    
    dict[@"event"] = eventName;
    dict[@"action"] = @(action);
    dict[@"subscribe_type"] = @([IMYRightsSDK sharedInstance].currentSubscribeType);
    dict[@"subscribe_gift"] = pricing.getAllGiftIDs;
    dict[@"subscribe_price"] = pricing.real_discount_price;
    dict[@"sub_price_id"] = @(pricing.id);
    dict[@"public_type"] = [IMYSubGuideManager biPublicTypeFromScnekey:self.sceneKey];
    dict[@"public_info"] = (IMYSubGuideManager.isFromVIPCenterTab ? @"是" : @"否");
    dict[@"info_key"] = @"通用";
    dict[@"public_key"] = @"通用";
    
    [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
}

- (void)refreshCurrentPricingOffsetX {
    if (!self.currentPricing) {
        return;
    }
    const NSUInteger index = [self.cellModel.vipInfo.pricing_list indexOfObject:self.currentPricing];
    if (index == NSNotFound) {
        return;
    }
    if (self.giftInfoView.imy_height < 1) {
        // 无赠礼
        return;
    }
    NSIndexPath *indexPath = [NSIndexPath indexPathForItem:index inSection:0];
    UICollectionViewLayoutAttributes *layout = [self.priceCollectionView layoutAttributesForItemAtIndexPath:indexPath];
    if (!layout) {
        // 还未布局完成, 0.1秒再次执行
        imy_asyncMainBlock(0.1, ^{
            [self refreshCurrentPricingOffsetX];
        });
        return;
    }
    // 赠礼View有12的偏移量
    self.currentPricingOffsetX = layout.center.x - 12;
    // 修正角标位置
    [self scrollViewDidScroll:self.priceCollectionView];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (scrollView != self.priceCollectionView) {
        return;
    }
    // 更新Arrow角标位置
    if (self.giftInfoView.imy_height > 1) {
        CGFloat arrowX = self.currentPricingOffsetX - scrollView.contentOffset.x;
        [self.giftInfoView updateWithArrowX:arrowX];
    }
}

#pragma mark - Public

- (BOOL)hasAgreeProto {
    return self.checkPrivateView.checkSeleted;
}

- (void)setAgreeProto:(BOOL)isAgree {
    self.checkPrivateView.checkSeleted = isAgree;
}

- (void)setCellModel:(IMYSubGuideVipInfoCellModel * const)cellModel
      currentPricing:(IMYSubGuidePricingListItem * const)currentPricing
            sceneKey:(NSString * const)sceneKey {
    // 如果参数完全一致，则无需刷新
    if (cellModel == _cellModel &&
        currentPricing == _currentPricing &&
        sceneKey == _sceneKey) {
        return;
    }
    _cellModel = cellModel;
    _currentPricing = currentPricing;
    _sceneKey = sceneKey;
    
    // 刷新价格列表
    [self.priceCollectionView reloadData];
    
    // 选中当前价格，并自动锚定
    [self selectPricing:currentPricing isCheck:YES needScroll:YES];
    
    // 修正角标位置
    imy_asyncMainBlock(^{
        [self refreshCurrentPricingOffsetX];
    });
}

- (IMYSubGuideVipPriceCell *)getPriceCellWithPricing:(IMYSubGuidePricingListItem *)currentPricing isRefresh:(BOOL)isRefresh {
    // 如果弹窗优惠卷跟当前选中的价格包不一致，则刷新当前价格选中框
    if (_currentPricing != currentPricing && isRefresh) {
        // 选中最新价格包
        _currentPricing = currentPricing;
        
        // 刷新价格列表
        [_priceCollectionView reloadData];
        
        // 选中当前价格，加个动画效果
        [UIView animateWithDuration:0.3 animations:^{
            [self selectPricing:currentPricing isCheck:NO needScroll:NO];
        }];
        
        NSIndexPath *indexPath = [self indexOfPricing:_currentPricing inPricings:_cellModel.vipInfo.pricing_list];
        if (indexPath && [_priceCollectionView numberOfItemsInSection:indexPath.section] > indexPath.item) {
            [_priceCollectionView scrollToItemAtIndexPath:indexPath
                                         atScrollPosition:UICollectionViewScrollPositionCenteredHorizontally
                                                 animated:NO];
        }
        
        // 修正角标位置
        imy_asyncMainBlock(^{
            [self refreshCurrentPricingOffsetX];
        });
    }
    NSIndexPath *indexPath = [self indexOfPricing:currentPricing inPricings:_cellModel.vipInfo.pricing_list];
    if (indexPath && [_priceCollectionView numberOfItemsInSection:indexPath.section] > indexPath.item) {
        IMYSubGuideVipPriceCell *cell = [_priceCollectionView cellForItemAtIndexPath:indexPath];
        return cell;
    }
    return nil;
}

- (CGFloat)cellHeight:(IMYSubGuideVipInfoCellModel *)cellModel
       currentPricing:(IMYSubGuidePricingListItem *)currentPricing
             sceneKey:(NSString *)sceneKey {
    [self setCellModel:cellModel
        currentPricing:currentPricing
              sceneKey:sceneKey];
    
    return [self cardHeight] + 8;
}

- (CGFloat)cardHeight {
    // 总高度
    CGFloat allHeight = 0;
    
    {
        // 价格区
        allHeight += (self.priceCardStyle == 1 ? 24 : 18);
        allHeight += [IMYSubGuideVipPriceCell showItemSizeWithStyle:self.priceCardStyle].height;
    }
    
    CGFloat const giftHeight = self.giftInfoView.imy_height;
    if (giftHeight > 0) {
        // 赠礼
        allHeight += 8;
        allHeight += giftHeight;
    }
    
    CGFloat const descHeight = self.descLabel.imy_height;
    if (descHeight > 0) {
        // 说明文案
        allHeight += 8;
        allHeight += descHeight;
    }
    
    {
        // 支付按钮
        allHeight += 8;
        allHeight += 48;
    }
    
    CGFloat const checkHeight = self.checkPrivateView.imy_size.height;
    if (checkHeight > 0) {
        // 权限检测
        allHeight += 8;
        allHeight += checkHeight;
    }
    
    {
        // 安全区
        allHeight += 12;
    }
    
    return allHeight;
}

#pragma mark - Get

- (UIView *)cardView {
    if (!_cardView) {
        _cardView = [UIView new];
        _cardView.frame = self.bounds;
        if (self.priceCardStyle == 1) {
            // 大卡片样式
            [_cardView imy_drawTopCornerRadius:12];
            
            _cardWhiteLayer = [CALayer layer];
            _cardWhiteLayer.backgroundColor = UIColor.whiteColor.CGColor;
            _cardWhiteLayer.frame = CGRectMake(0, 0, SCREEN_WIDTH, 187);
            [_cardView.layer addSublayer:_cardWhiteLayer];
            
            _cardGradientLayer = [CAGradientLayer layer];
            _cardGradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#FFFFFF").CGColor,
                                          (__bridge id)[IMY_COLOR_KEY(@"#FFFFFF") colorWithAlphaComponent:0].CGColor];
            _cardGradientLayer.locations = @[@0.0, @1.0];
            _cardGradientLayer.startPoint = CGPointMake(0, 0);
            _cardGradientLayer.endPoint = CGPointMake(0, 1);
            _cardGradientLayer.frame = CGRectMake(0, _cardWhiteLayer.frame.size.height, SCREEN_WIDTH, 105);
            [_cardView.layer addSublayer:_cardGradientLayer];
        } else {
            _cardView.backgroundColor = UIColor.whiteColor;
            [_cardView imy_drawAllCornerRadius:12];
        }
    }
    return _cardView;
}

- (UICollectionView *)priceCollectionView {
    if (!_priceCollectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.minimumInteritemSpacing = 8.0;
        layout.minimumLineSpacing = 8.0;
        layout.itemSize = [IMYSubGuideVipPriceCell showItemSizeWithStyle:self.priceCardStyle];
        layout.sectionInset = UIEdgeInsetsMake(0, 12, 0, 12);
        layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        
        CGFloat const boxWidth = (self.priceCardStyle == 1 ? SCREEN_WIDTH : SCREEN_WIDTH - 24);
        _priceCollectionView = [[UICollectionView alloc] initWithFrame:CGRectMake(0, 0, boxWidth, layout.itemSize.height)
                                                  collectionViewLayout:layout];
        _priceCollectionView.backgroundColor = [UIColor clearColor];
        _priceCollectionView.clipsToBounds = NO;
        _priceCollectionView.delegate = self;
        _priceCollectionView.dataSource = self;
        [_priceCollectionView registerClass:IMYSubGuideVipPriceCell.class forCellWithReuseIdentifier:@"IMYSubGuideVipPriceCell"];
        _priceCollectionView.showsHorizontalScrollIndicator = NO;
    }
    return _priceCollectionView;
}

- (IMYSubGuideVipGiftInfoView *)giftInfoView {
    if (!_giftInfoView) {
        CGFloat const boxWidth = (self.priceCardStyle == 1 ? SCREEN_WIDTH - 24 : SCREEN_WIDTH - 48);
        _giftInfoView = [IMYSubGuideVipGiftInfoView giftInfoViewWithBoxWidth:boxWidth];
        _giftInfoView.showStyle = self.priceCardStyle;
        _giftInfoView.sceneKey = self.sceneKey;
        _giftInfoView.arrowItemWidth = [IMYSubGuideVipPriceCell showItemSizeWithStyle:self.priceCardStyle].width;
        @weakify(self);
        _giftInfoView.onSelectItemDidChanged = ^{
            @strongify(self);
            [self refreshPayBtnShowText];
            if (self.onGiftItemsSelectedBlock) {
                self.onGiftItemsSelectedBlock(self.giftInfoView.selectedItems);
            }
        };
    }
    return _giftInfoView;
}

- (UILabel *)descLabel {
    if (!_descLabel) {
        _descLabel = [[UILabel alloc] init];
        _descLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        _descLabel.textColor = IMY_COLOR_KEY(kCK_Black_B);
        _descLabel.numberOfLines = 0;
    }
    return _descLabel;
}

- (IMYCapsuleButton *)payBtn {
    if (!_payBtn) {
        CGFloat const boxWidth = (self.priceCardStyle == 1 ? SCREEN_WIDTH - 24 : SCREEN_WIDTH - 48);
        _payBtn = [[IMYCapsuleButton alloc] initWithFrame:CGRectMake(0, 0, boxWidth, 48)];
        _payBtn.type = IMYButtonTypeFillRed;
        _payBtn.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        [_payBtn addTarget:self action:@selector(handlePayBtnEvent:) forControlEvents:UIControlEventTouchUpInside];
        
        [_payBtn setTitle:IMYString(@"确认协议并支付") forState:UIControlStateNormal];
        [_payBtn imy_drawAllCornerRadius:24];
        
        _payBtn.titleAtDirection = IMYDirectionCenterX | IMYDirectionCenterY;
        
        // 渐变背景色
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#C34DFF").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FF4D6A").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FFA64D").CGColor];
        gradientLayer.locations = @[@0.0, @0.5, @1.0];
        gradientLayer.startPoint = CGPointMake(0, 0);
        gradientLayer.endPoint = CGPointMake(1, 0);
        gradientLayer.frame = _payBtn.bounds;
        [_payBtn.layer insertSublayer:gradientLayer atIndex:0];
    }
    return _payBtn;
}

- (IMYSubGuideCheckPrivateView *)checkPrivateView {
    if (!_checkPrivateView) {
        _checkPrivateView = [[IMYSubGuideCheckPrivateView alloc] init];
        _checkPrivateView.checkSeleted = NO;
        _checkPrivateView.isAlignmentCenter = (self.priceCardStyle == 1 ? YES : NO);
        @weakify(self);
        [_checkPrivateView setDidChangedCheckState:^(BOOL seleted) {
            @strongify(self);
            if (self.onAgreeProtoDidChangedBlock) {
                self.onAgreeProtoDidChangedBlock(seleted);
            }
        }];
    }
    return _checkPrivateView;
}

@end
