//
//  IMYSGVipGiftExplainDialog.m
//  IMYBaseKit
//
//  Created by ljh on 2025/8/29.
//

#import "IMYSGVipGiftExplainDialog.h"
#import "IMYBaseKit.h"
#import "NSString+IMYFoundation.h"

@interface IMYSGVipGiftExplainDialog ()

@property (nonatomic, strong) UIView *backgroundView;
@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *contentLabel;
@property (nonatomic, strong) UIButton *closeButton;

@end

@implementation IMYSGVipGiftExplainDialog

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.frame = [UIScreen mainScreen].bounds;
    self.backgroundColor = [UIColor clearColor];

    // 背景遮罩
    _backgroundView = [[UIView alloc] init];
    _backgroundView.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.5];
    _backgroundView.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:_backgroundView];

    // 添加背景点击手势
    UITapGestureRecognizer *backgroundTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(backgroundTapped)];
    [_backgroundView addGestureRecognizer:backgroundTap];

    // 容器视图
    _containerView = [[UIView alloc] init];
    _containerView.backgroundColor = [UIColor whiteColor];
    _containerView.layer.cornerRadius = 12.0;
    _containerView.layer.masksToBounds = YES;
    _containerView.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:_containerView];

    // 关闭按钮
    _closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_closeButton setImage:[UIImage imageNamed:@"icon_close_gray"] forState:UIControlStateNormal];
    [_closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    _closeButton.translatesAutoresizingMaskIntoConstraints = NO;
    [_containerView addSubview:_closeButton];

    // 标题标签
    _titleLabel = [[UILabel alloc] init];
    _titleLabel.font = [UIFont boldSystemFontOfSize:18];
    _titleLabel.textColor = [UIColor blackColor];
    _titleLabel.textAlignment = NSTextAlignmentCenter;
    _titleLabel.numberOfLines = 0;
    _titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [_containerView addSubview:_titleLabel];

    // 内容标签
    _contentLabel = [[UILabel alloc] init];
    _contentLabel.font = [UIFont systemFontOfSize:14];
    _contentLabel.textColor = [UIColor colorWithRed:0.4 green:0.4 blue:0.4 alpha:1.0];
    _contentLabel.numberOfLines = 0;
    _contentLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [_containerView addSubview:_contentLabel];

    [self setupConstraints];
}

- (void)setupConstraints {
    // 背景视图约束
    [NSLayoutConstraint activateConstraints:@[
        [_backgroundView.topAnchor constraintEqualToAnchor:self.topAnchor],
        [_backgroundView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor],
        [_backgroundView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor],
        [_backgroundView.bottomAnchor constraintEqualToAnchor:self.bottomAnchor]
    ]];

    // 容器视图约束
    [NSLayoutConstraint activateConstraints:@[
        [_containerView.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
        [_containerView.centerYAnchor constraintEqualToAnchor:self.centerYAnchor],
        [_containerView.leadingAnchor constraintGreaterThanOrEqualToAnchor:self.leadingAnchor constant:40],
        [_containerView.trailingAnchor constraintLessThanOrEqualToAnchor:self.trailingAnchor constant:-40],
        [_containerView.widthAnchor constraintLessThanOrEqualToConstant:320]
    ]];

    // 关闭按钮约束
    [NSLayoutConstraint activateConstraints:@[
        [_closeButton.topAnchor constraintEqualToAnchor:_containerView.topAnchor constant:12],
        [_closeButton.trailingAnchor constraintEqualToAnchor:_containerView.trailingAnchor constant:-12],
        [_closeButton.widthAnchor constraintEqualToConstant:24],
        [_closeButton.heightAnchor constraintEqualToConstant:24]
    ]];

    // 标题标签约束
    [NSLayoutConstraint activateConstraints:@[
        [_titleLabel.topAnchor constraintEqualToAnchor:_containerView.topAnchor constant:20],
        [_titleLabel.leadingAnchor constraintEqualToAnchor:_containerView.leadingAnchor constant:20],
        [_titleLabel.trailingAnchor constraintEqualToAnchor:_containerView.trailingAnchor constant:-50]
    ]];

    // 内容标签约束
    [NSLayoutConstraint activateConstraints:@[
        [_contentLabel.topAnchor constraintEqualToAnchor:_titleLabel.bottomAnchor constant:16],
        [_contentLabel.leadingAnchor constraintEqualToAnchor:_containerView.leadingAnchor constant:20],
        [_contentLabel.trailingAnchor constraintEqualToAnchor:_containerView.trailingAnchor constant:-20],
        [_contentLabel.bottomAnchor constraintEqualToAnchor:_containerView.bottomAnchor constant:-20]
    ]];
}

- (void)setGiftItem:(IMYSubGuidePricingGiftInfoListItem *)giftItem {
    _giftItem = giftItem;
    [self updateContent];
}

- (void)updateContent {
    if (!_giftItem) {
        return;
    }

    // 设置标题
    _titleLabel.text = _giftItem.name ?: @"赠礼说明";

    // 设置内容 - 使用HTML解析
    if (_giftItem.desc.length > 0) {
        NSMutableAttributedString *attributedString = [_giftItem.desc imy_toHTMLAttributedString];
        if (attributedString) {
            // 调整字体和颜色以匹配设计
            [attributedString enumerateAttributesInRange:NSMakeRange(0, attributedString.length)
                                                 options:NSAttributedStringEnumerationLongestEffectiveRangeNotRequired
                                              usingBlock:^(NSDictionary<NSAttributedStringKey,id> * _Nonnull attrs, NSRange range, BOOL * _Nonnull stop) {
                NSMutableDictionary *adjustedAttrs = [NSMutableDictionary dictionaryWithDictionary:attrs];

                // 设置字体
                UIFont *font = adjustedAttrs[NSFontAttributeName];
                if (!font || font.pointSize > 16) {
                    adjustedAttrs[NSFontAttributeName] = [UIFont systemFontOfSize:14];
                }

                // 设置文字颜色
                UIColor *textColor = adjustedAttrs[NSForegroundColorAttributeName];
                if (!textColor) {
                    adjustedAttrs[NSForegroundColorAttributeName] = [UIColor colorWithRed:0.4 green:0.4 blue:0.4 alpha:1.0];
                }

                [attributedString setAttributes:adjustedAttrs range:range];
            }];

            _contentLabel.attributedText = attributedString;
        } else {
            _contentLabel.text = _giftItem.desc;
        }
    } else {
        _contentLabel.text = @"暂无说明";
    }
}

- (void)backgroundTapped {
    [self dismiss];
}

- (void)closeButtonTapped {
    [self dismiss];
}

- (void)show {
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    if (!keyWindow) {
        keyWindow = [UIApplication sharedApplication].windows.firstObject;
    }

    [keyWindow addSubview:self];

    // 初始状态
    self.alpha = 0;
    _containerView.transform = CGAffineTransformMakeScale(0.8, 0.8);

    // 显示动画
    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseOut animations:^{
        self.alpha = 1.0;
        self->_containerView.transform = CGAffineTransformIdentity;
    } completion:nil];
}

- (void)dismiss {
    [UIView animateWithDuration:0.25 delay:0 options:UIViewAnimationOptionCurveEaseIn animations:^{
        self.alpha = 0;
        self->_containerView.transform = CGAffineTransformMakeScale(0.8, 0.8);
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

@end
