//
//  IMYSGVipGiftExplainDialog.m
//  IMYBaseKit
//
//  Created by ljh on 2025/8/29.
//

#import "IMYSGVipGiftExplainDialog.h"
#import "IMYBaseKit.h"
#import "NSString+IMYFoundation.h"
#import <Masonry/Masonry.h>

@interface IMYSGVipGiftExplainDialog ()

@property (nonatomic, strong) UIView *backgroundView;
@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *contentLabel;
@property (nonatomic, strong) UIButton *closeButton;

// 约束引用，用于动画
@property (nonatomic, strong) MASConstraint *containerCenterYConstraint;

@end

@implementation IMYSGVipGiftExplainDialog

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.frame = [UIScreen mainScreen].bounds;
    self.backgroundColor = [UIColor clearColor];

    // 背景遮罩
    _backgroundView = [[UIView alloc] init];
    _backgroundView.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.5];
    [self addSubview:_backgroundView];

    // 添加背景点击手势
    UITapGestureRecognizer *backgroundTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(backgroundTapped)];
    [_backgroundView addGestureRecognizer:backgroundTap];

    // 容器视图
    _containerView = [[UIView alloc] init];
    _containerView.backgroundColor = [UIColor whiteColor];
    _containerView.layer.cornerRadius = 12.0;
    _containerView.layer.masksToBounds = YES;
    [self addSubview:_containerView];

    // 关闭按钮
    _closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_closeButton setImage:[UIImage imageNamed:@"icon_close_gray"] forState:UIControlStateNormal];
    [_closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [_containerView addSubview:_closeButton];

    // 标题标签
    _titleLabel = [[UILabel alloc] init];
    _titleLabel.font = [UIFont boldSystemFontOfSize:18];
    _titleLabel.textColor = [UIColor blackColor];
    _titleLabel.textAlignment = NSTextAlignmentCenter;
    _titleLabel.numberOfLines = 0;
    [_containerView addSubview:_titleLabel];

    // 内容标签
    _contentLabel = [[UILabel alloc] init];
    _contentLabel.font = [UIFont systemFontOfSize:14];
    _contentLabel.textColor = [UIColor colorWithRed:0.4 green:0.4 blue:0.4 alpha:1.0];
    _contentLabel.numberOfLines = 0;
    [_containerView addSubview:_contentLabel];

    [self setupConstraints];
}

- (void)setupConstraints {
    // 背景视图约束
    [_backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];

    // 容器视图约束
    [_containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        // 初始位置在屏幕下方，用于动画
        self.containerCenterYConstraint = make.centerY.equalTo(self.mas_bottom).offset(200);
        make.left.greaterThanOrEqualTo(self).offset(40);
        make.right.lessThanOrEqualTo(self).offset(-40);
        make.width.lessThanOrEqualTo(@320);
    }];

    // 关闭按钮约束
    [_closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_containerView).offset(12);
        make.right.equalTo(_containerView).offset(-12);
        make.size.mas_equalTo(CGSizeMake(24, 24));
    }];

    // 标题标签约束
    [_titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_containerView).offset(20);
        make.left.equalTo(_containerView).offset(20);
        make.right.equalTo(_containerView).offset(-50);
    }];

    // 内容标签约束
    [_contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_titleLabel.mas_bottom).offset(16);
        make.left.equalTo(_containerView).offset(20);
        make.right.equalTo(_containerView).offset(-20);
        make.bottom.equalTo(_containerView).offset(-20);
    }];
}

- (void)setGiftItem:(IMYSubGuidePricingGiftInfoListItem *)giftItem {
    _giftItem = giftItem;
    [self updateContent];
}

- (void)updateContent {
    if (!_giftItem) {
        return;
    }

    // 设置标题
    _titleLabel.text = _giftItem.name ?: @"赠礼说明";

    // 设置内容 - 使用HTML解析
    if (_giftItem.desc.length > 0) {
        NSMutableAttributedString *attributedString = [_giftItem.desc imy_toHTMLAttributedString];
        if (attributedString) {
            // 调整字体和颜色以匹配设计
            [attributedString enumerateAttributesInRange:NSMakeRange(0, attributedString.length)
                                                 options:NSAttributedStringEnumerationLongestEffectiveRangeNotRequired
                                              usingBlock:^(NSDictionary<NSAttributedStringKey,id> * _Nonnull attrs, NSRange range, BOOL * _Nonnull stop) {
                NSMutableDictionary *adjustedAttrs = [NSMutableDictionary dictionaryWithDictionary:attrs];

                // 设置字体
                UIFont *font = adjustedAttrs[NSFontAttributeName];
                if (!font || font.pointSize > 16) {
                    adjustedAttrs[NSFontAttributeName] = [UIFont systemFontOfSize:14];
                }

                // 设置文字颜色
                UIColor *textColor = adjustedAttrs[NSForegroundColorAttributeName];
                if (!textColor) {
                    adjustedAttrs[NSForegroundColorAttributeName] = [UIColor colorWithRed:0.4 green:0.4 blue:0.4 alpha:1.0];
                }

                [attributedString setAttributes:adjustedAttrs range:range];
            }];

            _contentLabel.attributedText = attributedString;
        } else {
            _contentLabel.text = _giftItem.desc;
        }
    } else {
        _contentLabel.text = @"暂无说明";
    }
}

- (void)backgroundTapped {
    [self dismiss];
}

- (void)closeButtonTapped {
    [self dismiss];
}

- (void)show {
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    if (!keyWindow) {
        keyWindow = [UIApplication sharedApplication].windows.firstObject;
    }

    [keyWindow addSubview:self];

    // 初始状态：背景透明，容器在屏幕下方
    self.alpha = 0;

    // 更新约束到最终位置
    [self.containerCenterYConstraint uninstall];
    [_containerView mas_updateConstraints:^(MASConstraintMaker *make) {
        self.containerCenterYConstraint = make.centerY.equalTo(self);
    }];

    // 执行约束动画
    [UIView animateWithDuration:0.4 delay:0 usingSpringWithDamping:0.8 initialSpringVelocity:0.5 options:UIViewAnimationOptionCurveEaseOut animations:^{
        self.alpha = 1.0;
        [self layoutIfNeeded];
    } completion:nil];
}

- (void)dismiss {
    // 更新约束到隐藏位置
    [self.containerCenterYConstraint uninstall];
    [_containerView mas_updateConstraints:^(MASConstraintMaker *make) {
        self.containerCenterYConstraint = make.centerY.equalTo(self.mas_bottom).offset(200);
    }];

    // 执行约束动画
    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseIn animations:^{
        self.alpha = 0;
        [self layoutIfNeeded];
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

@end
