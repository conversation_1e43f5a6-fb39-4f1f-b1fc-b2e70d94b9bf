//
//  IMYSGVipGiftExplainDialog.m
//  IMYBaseKit
//
//  Created by ljh on 2025/8/29.
//

#import "IMYSGVipGiftExplainDialog.h"
#import "IMYBaseKit.h"
#import "NSString+IMYFoundation.h"
#import <Masonry/Masonry.h>

@interface IMYSGVipGiftExplainDialog ()

@property (nonatomic, strong) UIView *backgroundView;
@property (nonatomic, strong) UIView *cardView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *contentLabel;
@property (nonatomic, strong) UIButton *closeButton;

@end

@implementation IMYSGVipGiftExplainDialog

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.backgroundView];
    [self addSubview:self.cardView];

    [self.cardView addSubview:self.titleLabel];
    [self.cardView addSubview:self.closeButton];
    [self.cardView addSubview:self.contentLabel];

    [self setupConstraints];
    [self setupGestures];
}

- (void)setupConstraints {
    [self.backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self);
    }];

    [self.cardView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.mas_equalTo(self);
        make.height.mas_lessThanOrEqualTo([UIScreen mainScreen].bounds.size.height - 177);
    }];

    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.cardView).offset(20);
        make.centerX.mas_equalTo(self.cardView);
        make.height.mas_equalTo(24);
    }];

    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.cardView).offset(15);
        make.right.mas_equalTo(self.cardView).offset(-14);
        make.size.mas_equalTo(CGSizeMake(32, 32));
    }];

    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLabel.mas_bottom).offset(16);
        make.left.mas_equalTo(self.cardView).offset(20);
        make.right.mas_equalTo(self.cardView).offset(-20);
        make.bottom.mas_equalTo(self.cardView).offset(-20);
    }];
}

- (void)setupGestures {
    UITapGestureRecognizer *backgroundTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(backgroundTapped)];
    [self.backgroundView addGestureRecognizer:backgroundTap];
}

- (void)setGiftItem:(IMYSubGuidePricingGiftInfoListItem *)giftItem {
    _giftItem = giftItem;
    [self updateContent];
}

- (void)updateContent {
    if (!_giftItem) {
        return;
    }

    // 设置标题
    self.titleLabel.text = _giftItem.name ?: @"赠礼说明";

    // 设置内容 - 使用HTML解析
    if (_giftItem.desc.length > 0) {
        NSMutableAttributedString *attributedString = [_giftItem.desc imy_toHTMLAttributedString];
        if (attributedString) {
            // 调整字体和颜色以匹配设计
            [attributedString enumerateAttributesInRange:NSMakeRange(0, attributedString.length)
                                                 options:NSAttributedStringEnumerationLongestEffectiveRangeNotRequired
                                              usingBlock:^(NSDictionary<NSAttributedStringKey,id> * _Nonnull attrs, NSRange range, BOOL * _Nonnull stop) {
                NSMutableDictionary *adjustedAttrs = [NSMutableDictionary dictionaryWithDictionary:attrs];

                // 设置字体
                UIFont *font = adjustedAttrs[NSFontAttributeName];
                if (!font || font.pointSize > 16) {
                    adjustedAttrs[NSFontAttributeName] = [UIFont systemFontOfSize:14];
                }

                // 设置文字颜色
                UIColor *textColor = adjustedAttrs[NSForegroundColorAttributeName];
                if (!textColor) {
                    adjustedAttrs[NSForegroundColorAttributeName] = [UIColor imy_colorForKey:kCK_Black_M];
                }

                [attributedString setAttributes:adjustedAttrs range:range];
            }];

            self.contentLabel.attributedText = attributedString;
        } else {
            self.contentLabel.text = _giftItem.desc;
        }
    } else {
        self.contentLabel.text = @"暂无说明";
    }
}

- (void)backgroundTapped {
    [self dismiss];
}

- (void)closeButtonTapped {
    [self dismiss];
}

- (void)show {
    self.frame = [UIScreen mainScreen].bounds;

    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    if (!keyWindow) {
        keyWindow = [UIApplication sharedApplication].windows.firstObject;
    }
    [keyWindow addSubview:self];

    // 初始状态
    self.backgroundView.alpha = 0;
    self.cardView.transform = CGAffineTransformMakeTranslation(0, self.cardView.frame.size.height);

    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseOut animations:^{
        self.backgroundView.alpha = 1;
        self.cardView.transform = CGAffineTransformIdentity;
    } completion:nil];
}

- (void)dismiss {
    [UIView animateWithDuration:0.25 delay:0 options:UIViewAnimationOptionCurveEaseIn animations:^{
        self.backgroundView.alpha = 0;
        self.cardView.transform = CGAffineTransformMakeTranslation(0, self.cardView.frame.size.height);
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

#pragma mark - Lazy Loading

- (UIView *)backgroundView {
    if (!_backgroundView) {
        _backgroundView = [[UIView alloc] init];
        [_backgroundView imy_addThemeActionBlock:^(UIView *weakObject) {
            if (IMYPublicAppHelper.shareAppHelper.isNight) {
                weakObject.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
            } else {
                weakObject.backgroundColor = [[UIColor imy_colorForKey:kCK_Black_A] colorWithAlphaComponent:0.4];
            }
        } forKey:@"setBackgroundColor"];
    }
    return _backgroundView;
}

- (UIView *)cardView {
    if (!_cardView) {
        _cardView = [[UIView alloc] init];
        [_cardView imy_setBackgroundColorForKey:kCK_White_AT];
        _cardView.layer.cornerRadius = 16;
        _cardView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        _cardView.layer.masksToBounds = YES;
    }
    return _cardView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.text = @"赠礼说明";
        _titleLabel.font = [UIFont boldSystemFontOfSize:18];
        [_titleLabel imy_setTextColor:kCK_Black_A];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

- (UIButton *)closeButton {
    if (!_closeButton) {
        _closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_closeButton imy_setImage:[UIImage imy_imageForKey:@"search_pop_close_btn"]];
        [_closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeButton;
}

- (UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [[UILabel alloc] init];
        _contentLabel.font = [UIFont systemFontOfSize:14];
        [_contentLabel imy_setTextColor:kCK_Black_M];
        _contentLabel.numberOfLines = 0;
    }
    return _contentLabel;
}

@end
