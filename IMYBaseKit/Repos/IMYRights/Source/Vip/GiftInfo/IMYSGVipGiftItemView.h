//
//  IMYSGVipGiftItemView.h
//  IMYBaseKit
//
//  Created by ljh on 2025/8/29.
//

#import <UIKit/UIKit.h>
#import "IMYSubGuideVipInfo.h"

NS_ASSUME_NONNULL_BEGIN

@interface IMYSGVipGiftItemView : UIView

/// 卡片宽度
@property (nonatomic, assign) NSInteger cardWidth;

/// 样式：1：单个赠礼，2：2个赠礼，3：超过2个赠礼
@property (nonatomic, assign) NSInteger cardStyle;

/// 选中状态 : 0 ：无选中框（选中），1：有选择框（选中），2：有选择框（未选中）
@property (nonatomic, assign) NSInteger isSelected;

/// 赠礼项
@property (nonatomic, strong) IMYSubGuidePricingGiftInfoListItem *giftItem;

@end

NS_ASSUME_NONNULL_END
