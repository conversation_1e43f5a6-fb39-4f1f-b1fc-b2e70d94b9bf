//
//  IMYSubGuideManager.m
//  demo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/31.
//

#import "IMYSubGuideManager.h"
#import "IMYBaseKit.h"
#import "IMYSubGuidePayRetView.h"
#import "IMYSubGuidePayRetSuccessView.h"
#import "IMYSubGuidePayRetCancelledView.h"
#import "IMYSubGuideUnlockView.h"
#import <IOC-Protocols/IOCAppInfo.h>

/// 支付结果通知
NSString * const KRightsSubGuideRaymentResultNotification = @"KRightsSubGuideRaymentResultNotification";
/// 优惠价变化通知（倒计时结束等）
NSString * const KRightsSubGuidePromotionChangedNotification = @"KRightsSubGuidePromotionChangedNotification";

/// 桌面小组件
NSString * const KRightsSceneKey_widget = @"widget";
/// 会员tab-桌面小组件
NSString * const KRightsSceneKey_vip_tab_widget = @"vip_tab_widget";
/// 智能分析
NSString * const KRightsSceneKey_ai_analysis = @"ai_analysis";
/// 会员中心
NSString * const KRightsSceneKey_vip_center = @"vip_center";
/// 会员tab-智能分析
NSString * const KRightsSceneKey_vip_tab_ai_analysis = @"vip_tab_ai_analysis";
/// 快速问诊
NSString * const KRightsSceneKey_kuaisuwenzhen = @"kuaisuwenzhen";
/// 报告解读
NSString * const KRightsSceneKey_baogaojiedu = @"baogaojiedu";
/// 备孕咨询
NSString * const KRightsSceneKey_beiyunzixun = @"beiyunzixun";
/// 专业测评
NSString * const KRightsSceneKey_zhuanyepingce = @"zhuanyepingce";
/// 话费充值页
NSString * const KRightsSceneKey_huafeichongzhi = @"huafeichongzhi";
/// 外卖红包页
NSString * const KRightsSceneKey_waimaihongbao = @"waimaihongbao";
/// 购物返现页
NSString * const KRightsSceneKey_gouwufanxian = @"gouwufanxian";
/// 更多权益页
NSString * const KRightsSceneKey_gengduoquanyi = @"gengduoquanyi";
/// 我tab
NSString * const KRightsSceneKey_my_tab = @"my_tab";
/// 权益详情页
NSString * const KRightsSceneKey_vip_details = @"vip_details";
/// 钱包页
NSString * const KRightsSceneKey_vip_wallet = @"wallet";
/// 开屏广告
NSString * const KRightsSceneKey_vip_ad = @"ad";
/// 桌面小组件-付费小组件
NSString * const KRightsSceneKey_widget_card = @"widget_card";
/// 返现红包页
NSString * const KRightsSceneKey_fanxianhongbao = @"fanxianhongbao";
/// 极简模式
NSString * const KRightsSceneKey_simple_mode = @"simple_mode";
/// 会员中心-头部卡片按钮
NSString * const KRightsSceneKey_vip_center_top = @"vip_center_top";
/// 会员中心-底部按钮
NSString * const KRightsSceneKey_vip_center_bottom = @"vip_center_bottom";
/// 网购商城页
NSString * const KRightsSceneKey_wanggoushangcheng = @"wanggoushangcheng";
/// 服务商城页
NSString * const KRightsSceneKey_fuwushangcheng = @"fuwushangcheng";
/// 视频会员充值页
NSString * const KRightsSceneKey_shipinhuiyuanchongzhi = @"shipinhuiyuanchongzhi";
/// 大姨妈具体时间记录-记录tab
NSString * const KRightsSceneKey_yima_time_recordtab = @"yima_time_recordtab";
/// 大姨妈具体时间记录-分析列表页
NSString * const KRightsSceneKey_yima_time_analysis = @"yima_time_analysis";
/// 大姨妈具体时间记录-首页
NSString * const KRightsSceneKey_yima_time_home = @"yima_time_home";
/// 黄金受孕期
NSString * const KRightsSceneKey_beiyun_golden = @"beiyun_golden";
/// 会员tab-黄金受孕期
NSString * const KRightsSceneKey_vip_tab_beiyun_golden = @"vip_tab_beiyun_golden";
/// 省钱商详页
NSString * const KRightsSceneKey_shengqianshangxiangye = @"shengqianshangxiangye";
/// 会员tab-专业测评
NSString * const KRightsSceneKey_vip_zhuanyepingce = @"vip_zhuanyepingce";
/// 会员tab-极简模式
NSString * const KRightsSceneKey_vip_simple_mode = @"vip_simple_mode";
/// 宝宝记-长视频
NSString * const KRightsSceneKey_bbj_long_video = @"bbj_long_video";
/// 美团联名券
NSString * const KRightsSceneKey_meituanlianmingquan = @"meituanlianmingquan";
/// 喂养记录智能分析
NSString * const KRightsSceneKey_lama_feeding_record = @"lama_feeding_record";
/// 专家课程
NSString * const KRightsSceneKey_byunyu_special_course = @"yunyu_special_course";
/// 会员tab-专家课程
NSString * const KRightsSceneKey_vip_special_course = @"vip_special_course";
/// 会员tab-喂养记录智能分析
NSString * const KRightsSceneKey_vip_feeding_record = @"vip_feeding_record";
/// 互医-快速问
NSString * const KRightsSceneKey_quick_ask = @"quick_ask";
/// 互医-定向问
NSString * const KRightsSceneKey_direct_ask = @"direct_ask";
/// 会员tab-省钱权益
NSString * const KRightsSceneKey_vip_shengqian = @"vip_shengqian";
/// 产检智能解读-产检时间表
NSString * const KRightsSceneKey_ai_chanjian_time = @"ai_chanjian_time";
/// 产检智能解读-小工具
NSString * const KRightsSceneKey_ai_chanjian_tool = @"ai_chanjian_tool";
/// 产检智能解读-会员tab
NSString * const KRightsSceneKey_ai_chanjian_vip = @"ai_chanjian_vip";


/// 支付页面返回通知
NSString * const kIMYVIPPaymentViewGobackNotificationName = @"kIMYVIPPaymentViewGobackNotificationName";

#pragma mark - 订阅管理

@interface IMYSubGuideManager ()

@property (nonatomic, strong) RACDisposable *guideConfigDisposable;
@property (nonatomic, strong) RACDisposable *rightInfoDisposable;

@end

@implementation IMYSubGuideManager

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static IMYSubGuideManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super allocWithZone:NULL] init];
    });
    return instance;
}

+ (id)allocWithZone:(struct _NSZone *)zone {
    return [self sharedInstance];
}

#pragma mark - Public

/// 订阅引导-引导配置
- (void)loadGuideConfigWithScene:(NSString *)sceneKey
                         options:(NSDictionary *)options
                         success:(void (^)(IMYSubGuideConfig * _Nonnull))successBlock
                           error:(void (^)(NSError * _Nonnull))errorBlock {
    if (!successBlock || !errorBlock) {
        NSAssert(NO, @"无效的回调参数");
        return;
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"scene_key"] = sceneKey;
    // 当前孕周
    NSInteger const currentPregnenyWeek = IMYHIVE_BINDER(IOCAppInfo).currentPregnenyWeek;
    if (currentPregnenyWeek >= 0) {
        params[@"pregnancy_week"] = @(currentPregnenyWeek);
    }
    // 当前最小宝宝出生日
    NSString * const birthdayString = [IMYHIVE_BINDER(IOCAppInfo).currentLatestBabyBirthday imy_getOnlyDateString];
    if (birthdayString.length > 0) {
        params[@"min_baby_birthday"] = birthdayString;
    }
    // 追加业务参数
    if (options.count > 0) {
        [params addEntriesFromDictionary:options];
    }
    
    [self.guideConfigDisposable dispose];
    self.guideConfigDisposable = [[IMYServerRequest getPath:@"/v3/sub/guide_config"
                                                       host:sub_seeyouyima_com
                                                     params:params
                                                    headers:nil] subscribeNext:^(IMYHTTPResponse *x) {
        NSDictionary * const dict = x.responseObject;
        IMYSubGuideConfig *guideConfig = nil;
        if (dict.count > 0) {
            guideConfig = [dict toModel:IMYSubGuideConfig.class];
        }
        if (guideConfig != nil) {
            successBlock(guideConfig);
        } else {
            if (errorBlock) {
                errorBlock([NSError errorWithDomain:@"data invalid!" code:-1005 userInfo:nil]);
            }
        }
    } error:errorBlock];
}

/// 订阅引导-会员详情
- (void)loadVipInfoWithSceneKey:(NSString *)sceneKey
                        options:(NSDictionary *)options
                        success:(void (^)(IMYSubGuideVipInfo *))successBlock
                          error:(void (^)(NSError *))errorBlock {
    id<IMYHTTPBuildable> buildable = [IMYServerRequest buildable];
    buildable.Host(sub_seeyouyima_com).Method(HTTPMethodGet).Path(@"/v3/sub/vip_info").RequestSerializerType(IMYHTTPSerializerTypeJSON);
    
    NSMutableDictionary *paramters = [NSMutableDictionary dictionary];
    [paramters imy_setNonNilObject:sceneKey forKey:@"scene_key"];
    [paramters addEntriesFromDictionary:options];
    buildable.Parameters(paramters);
    
    [buildable.signal subscribeNext:^(id<IMYHTTPResponse> response) {
        id responseObj = response.responseObject;
        IMYSubGuideVipInfo *vipInfo = nil;
        if (responseObj && [responseObj isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dict = response.responseObject;
            vipInfo = [dict toModel:IMYSubGuideVipInfo.class];
        }
        
        // 强制要求有价格数组才算成功
        if (vipInfo.pricing_list.count > 0) {
            if (successBlock) {
                successBlock(vipInfo);
            }
        } else {
            if (errorBlock) {
                errorBlock([NSError errorWithDomain:@"data invalid!" code:-1005 userInfo:nil]);
            }
        }
    } error:^(NSError *error) {
        if (errorBlock) {
            errorBlock(error);
        }
    }];
}

/// 订阅引导-分组权益详情
- (void)loadRightsInfoWithSceneKey:(NSString *)sceneKey success:(void (^)(NSArray<IMYSubGuideRightInfo *> *rightInfos, NSDictionary *actionInfo))successBlock error:(void (^)(NSError *error))errorBlock;
 {
    [self.rightInfoDisposable dispose];
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:1];
    params[@"scene_key"] = sceneKey;
    self.rightInfoDisposable = [[IMYServerRequest getPath:@"/v3/sub/rights_info"
                                                     host:sub_seeyouyima_com
                                                   params:params headers:nil] subscribeNext:^(IMYHTTPResponse *x) {
        id responseObj = x.responseObject;
        if (responseObj && [responseObj isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dict = responseObj;
            if (dict && [dict isKindOfClass:[NSDictionary class]]) {
                NSArray *rights_group_list = dict[@"rights_group_list"];
                NSMutableDictionary *actionInfo = [NSMutableDictionary dictionary];
                if (dict[@"btn_text"] && dict[@"btn_jump_url"]) {
                    actionInfo[@"btn_text"] = dict[@"btn_text"];
                    actionInfo[@"btn_jump_url"] = dict[@"btn_jump_url"];
                }
                NSArray<IMYSubGuideRightInfo *> *rightInfos = [rights_group_list toModels:[IMYSubGuideRightInfo class]];
                if (successBlock) {
                    successBlock(rightInfos,actionInfo);
                }
                return;
            }
            
            if (successBlock) {
                successBlock(@[],nil);
            }
        } else {
            if (errorBlock) {
                errorBlock([NSError errorWithDomain:@"data invalid!" code:-1005 userInfo:nil]);
            }
        }
    } error:^(NSError *error) {
        if (errorBlock) {
            errorBlock(error);
        }
    }];
}

/// 支付流程
- (void)payWithSession:(IMYSubGuideSession *)session {
    NSString * const planId = [NSString stringWithFormat:@"%ld", session.planId];
    NSString * const priceId = [NSString stringWithFormat:@"%ld", session.priceId];
    
    NSMutableDictionary *appendParams = [NSMutableDictionary dictionary];
    if ([IMYSubGuideManager isFromVIPCenterTab]) {
        [appendParams imy_setNonNilObject:@1 forKey:@"is_vip_tab"];
    } else {
        [appendParams imy_setNonNilObject:@0 forKey:@"is_vip_tab"];
    }
    [appendParams imy_setNonNilObject:session.sceneKey forKey:@"scene_key"];
    [appendParams imy_setNonNilObject:session.gift_info forKey:@"gift_info"];
    [appendParams imy_setNonNilObject:session.gift_promotion_ids forKey:@"gift_promotion_ids"];
    
    // 优惠劵ID
    if (session.user_promotion_id > 0) {
        appendParams[@"user_promotion_id"] = @(session.user_promotion_id);
    } else if (session.currentPricing.countdownTime > 0) {
        appendParams[@"user_promotion_id"] = @(session.currentPricing.promotion_info.user_promotion_id);
    }
    
    // 增加显示的价格
    appendParams[@"amount"] = session.currentPricing.discount_price;
    
    // 当前页面堆栈（服务端埋点用）
    appendParams[@"main_tab"] = IMYMeetyouHTTPHooks.currentMainTab;
    
    if (session.quick_order > 0) {
        // 快速下单
        appendParams[@"quick_order"] = @(session.quick_order);
        // 价格包类型
        if (session.currentPricing.sub_type > 0) {
            appendParams[@"sub_type"] = @(session.currentPricing.sub_type);
        }
        // 当前孕周
        NSInteger const currentPregnenyWeek = IMYHIVE_BINDER(IOCAppInfo).currentPregnenyWeek;
        if (currentPregnenyWeek >= 0) {
            appendParams[@"pregnancy_week"] = @(currentPregnenyWeek);
        }
        // 当前最小宝宝出生日
        NSString * const birthdayString = [IMYHIVE_BINDER(IOCAppInfo).currentLatestBabyBirthday imy_getOnlyDateString];
        if (birthdayString.length > 0) {
            appendParams[@"min_baby_birthday"] = birthdayString;
        }
    }
    
    // 非恢复订单
    session.isFromRestore = NO;
    
    [[IMYRightsSDK sharedInstance] payWithPlanId:planId
                                         priceId:priceId
                                    appendParams:appendParams
                                   progressBlock:^(NSDictionary *result, NSInteger state) {
        NSLog(@"iap-event-progressBlock-result === %@", result);
        NSLog(@"iap-event-progressBlock-state === %ld", state);
        
        if (state == 0) {
            // [dy_zdxf]：订阅_自动续费 曝光
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            [dict imy_setNonNilObject:@"dy_zdxf" forKey:@"event"];
            [dict imy_setNonNilObject:@1 forKey:@"action"];
            [dict imy_setNonNilObject:@([IMYRightsSDK sharedInstance].currentSubscribeType) forKey:@"subscribe_type"];
            [dict imy_setNonNilObject:[IMYSubGuideManager biPublicTypeFromScnekey:session.sceneKey] forKey:@"public_type"];
            [dict imy_setNonNilObject:(IMYSubGuideManager.isFromVIPCenterTab ? @"是" : @"否") forKey:@"public_info"];
            [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
        }
        
    } completedBlock:^(NSDictionary * const result, NSError * const error) {
        // session不再存储Block
        void(^ const onPayUserCancelBlock)(UIView<IMYAlertShowViewProtocol> *) = [session.onPayUserCancelBlock copy];
        session.onPayUserCancelBlock = nil;
        
        // 赋值结果
        session.isSuccess = (!error ? YES : NO);
        
        // [dy_zdxf]：订阅_自动续费 点击
        if (!error) {
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            [dict imy_setNonNilObject:@"dy_zdxf" forKey:@"event"];
            [dict imy_setNonNilObject:@2 forKey:@"action"];
            [dict imy_setNonNilObject:@([IMYRightsSDK sharedInstance].currentSubscribeType) forKey:@"subscribe_type"];
            [dict imy_setNonNilObject:[IMYSubGuideManager biPublicTypeFromScnekey:session.sceneKey] forKey:@"public_type"];
            [dict imy_setNonNilObject:(IMYSubGuideManager.isFromVIPCenterTab ? @"是" : @"否") forKey:@"public_info"];
            [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
        }
        
        if (error) {
            // [dy_dysb]：订阅_订阅失败
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            [dict imy_setNonNilObject:@"dy_dysb" forKey:@"event"];
            [dict imy_setNonNilObject:@2 forKey:@"action"];
            [dict imy_setNonNilObject:@([IMYRightsSDK sharedInstance].currentSubscribeType) forKey:@"subscribe_type"];
            [dict imy_setNonNilObject:[IMYSubGuideManager biPublicTypeFromScnekey:session.sceneKey] forKey:@"public_type"];
            [dict imy_setNonNilObject:(IMYSubGuideManager.isFromVIPCenterTab ? @"是" : @"否") forKey:@"public_info"];
            // subscribe_price
            NSString *amount = result[@"order"][@"amount"];
            [dict imy_setNonNilObject:amount forKey:@"subscribe_price"];
            // 如果无业务价格包id，则取订单接口的返回值
            NSInteger sub_price_id = priceId.integerValue;
            if (sub_price_id == 0) {
                sub_price_id = [result[@"order"][@"pricing_package_id"] integerValue];
            }
            [dict imy_setNonNilObject:@(sub_price_id) forKey:@"sub_price_id"];
            // subscribe_order_id
            NSString *transaction_id = result[@"order"][@"transaction_id"];
            [dict imy_setNonNilObject:transaction_id forKey:@"subscribe_order_id"];
            [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
            
            if (error.code == -101) {
                // 人为取消，挽留弹窗
                [self showPayRetWithSession:session onCancelBlock:onPayUserCancelBlock];
            } else {
                // 系统原因，直接回调
                imy_asyncMainBlock(^{
                    BOOL const isVerifying = [result[@"isVerifying"] boolValue];
                    
                    NSDictionary *apiErrorBody = [[error.userInfo[NSUnderlyingErrorKey] af_responseData] imy_jsonObject];
                    NSString *apiMessage = apiErrorBody[@"message"];
                    NSInteger apiCode = [apiErrorBody[@"code"] integerValue];
                    
                    [[NSNotificationCenter defaultCenter] postNotificationName:KRightsSubGuideRaymentResultNotification
                                                                        object:@{
                        @"isSuccess": @(NO),
                        @"isVerifying": @(isVerifying),
                        @"isOfferExpires": @(apiCode == 4018),
                    }];
                    
                    if (session.onPayCompletedBlock) {
                        session.onPayCompletedBlock(NO, NO);
                    }
                    
                    if (imy_isEmptyString(apiMessage)) {
                        if (!isVerifying) {
                            if (error.code == -3 || error.code == -1 || error.code == -100) {
                                // 获取不到商品信息，苹果系统不支持购买，苹果系统支付失败，都弹内部文案
                                apiMessage = IMYString(error.domain);
                            } else {
                                apiMessage = IMYString(@"购买失败，重新去订阅");
                            }
                        } else {
                            if (error.code == -1) {
                                apiMessage = IMYString(error.domain);
                            } else {
                                apiMessage = [IMYNetState networkEnable] ? IMYString(@"网络缓慢，请稍后再试") : IMYString(@"网络不见了，请检查网络");
                            }
                        }
                    }
                    if (imy_isEmptyString(apiMessage)) {
                        apiMessage = IMYString(@"网络缓慢，请稍后再试");
                    }
                    [UIWindow imy_showTextHUD:apiMessage];
                });
            }
            
        } else {
            // [dy_dycg]：订阅_订阅成功
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            [dict imy_setNonNilObject:@"dy_dycg" forKey:@"event"];
            [dict imy_setNonNilObject:@2 forKey:@"action"];
            [dict imy_setNonNilObject:@([IMYRightsSDK sharedInstance].currentSubscribeType) forKey:@"subscribe_type"];
            [dict imy_setNonNilObject:[IMYSubGuideManager biPublicTypeFromScnekey:session.sceneKey] forKey:@"public_type"];
            [dict imy_setNonNilObject:(IMYSubGuideManager.isFromVIPCenterTab ? @"是" : @"否") forKey:@"public_info"];
            // subscribe_price
            NSString *amount = result[@"order"][@"amount"];
            [dict imy_setNonNilObject:amount forKey:@"subscribe_price"];
            // 如果无业务价格包id，则取订单接口的返回值
            NSInteger sub_price_id = priceId.integerValue;
            if (sub_price_id == 0) {
                sub_price_id = [result[@"order"][@"pricing_package_id"] integerValue];
            }
            [dict imy_setNonNilObject:@(sub_price_id) forKey:@"sub_price_id"];
            // subscribe_order_id
            NSString *transaction_id = result[@"order"][@"transaction_id"];
            [dict imy_setNonNilObject:transaction_id forKey:@"subscribe_order_id"];
            [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
            
            // 支付成功，并且有新样式信息
            NSDictionary *popInfo = result[@"verify"][@"pop_info"];
            if (popInfo.count > 0 && ![session.sceneKey isEqualToString:@"yima_time_analysis"]) {
                [self showPayRetWithPopInfo:popInfo session:session];
            } else { // 标准弹窗
                [self showPayRetWithSession:session onCancelBlock:nil];
            }
        }
    }];
}

/// 恢复权益
- (void)restoreWithSession:(IMYSubGuideSession *)session {
    NSMutableDictionary *appendParams = [NSMutableDictionary dictionary];
    if ([IMYSubGuideManager isFromVIPCenterTab]) {
        [appendParams imy_setNonNilObject:@1 forKey:@"is_vip_tab"];
    } else {
        [appendParams imy_setNonNilObject:@0 forKey:@"is_vip_tab"];
    }
    [appendParams imy_setNonNilObject:session.sceneKey forKey:@"scene_key"];
    
    session.isFromRestore = YES;
    
    [[IMYRightsSDK sharedInstance] restoreWithAppendParams:appendParams
                                             progressBlock:^(NSDictionary *result, NSInteger state) {
        
    } completedBlock:^(NSDictionary *result, NSError *error) {
        NSString *transaction_id = result[@"verify"][@"transaction_id"];
        if (!transaction_id.length) {
            transaction_id = result[@"order"][@"transaction_id"];
        }
        imy_asyncMainBlock(^{
            // 赋值结果
            session.isSuccess = (!error ? YES : NO);
            // 弹失败弹窗
            if (error) {
                IMYFreestyleAlertBox *box = [IMYFreestyleAlertBox new];
                box.title = @"恢复失败";
                box.detail = @"未查询到可用的订阅，请您核实是否存在未到账订单，或稍后再试";
                box.agree = @"我知道了";
                box.onActionBlock = ^(IMYFSAlertAction action) {
                    if (action == IMYFSAlertActionAgree) {
                    }
                };
                [box show];
                
                // 存在服务端的 message，则上层再弹一个 Toast 告知用户原因
                BOOL const isVerifying = [result[@"isVerifying"] boolValue];
                NSString *apiMessage = [[error af_responseData] imy_jsonObject][@"message"];
                if (isVerifying && error.code == -1) {
                    apiMessage = IMYString(error.domain);
                }
                if (imy_isNotEmptyString(apiMessage)) {
                    [UIWindow imy_showTextHUD:apiMessage];
                }
                
                // 订单恢复失败埋点
                [IMYGAEventHelper postWithPath:@"/event" params:@{
                    @"action" : @2,
                    @"event" : @"dy_ios_wdzddhfsb",
                    @"subscribe_order_id" : transaction_id ?: @"",
                } headers:nil completed:nil];
            } else {
                // 弹恢复成功弹窗
                [self showPayRetWithSession:session onCancelBlock:nil];
                // 订单恢复成功埋点
                [IMYGAEventHelper postWithPath:@"/event" params:@{
                    @"action" : @2,
                    @"event" : @"dy_ios_wdzddhfcg",
                    @"subscribe_order_id" : transaction_id ?: @"",
                } headers:nil completed:nil];
            }
        });
    }];
}

/// 显示支付结果
- (void)showPayRetWithSession:(IMYSubGuideSession *)session onCancelBlock:(void(^)(IMYSubGuidePayRetView *))onCancelBlock {
    if (IMYRightsSDK.isSubAuditReview) {
        // 审核状态下无需弹窗
        if (session.isSuccess) {
            [UIWindow imy_showTextHUD:@"购买成功，欢迎您成为会员！"];
        }
        imy_asyncMainBlock(^{
            [[NSNotificationCenter defaultCenter] postNotificationName:KRightsSubGuideRaymentResultNotification
                                                                object:@{
                @"isSuccess": @(session.isSuccess),
            }];
            
            if (session.onPayCompletedBlock) {
                session.onPayCompletedBlock(session.isSuccess, NO);
            }
        });
        return;
    }
    
    // 业务禁用支付挽留弹窗
    if (session.disable_alerts == 1) {
        imy_asyncMainBlock(^{
            [[NSNotificationCenter defaultCenter] postNotificationName:KRightsSubGuideRaymentResultNotification
                                                                object:@{
                @"isSuccess": @(NO),
            }];
            
            if (session.onPayCompletedBlock) {
                session.onPayCompletedBlock(NO, NO);
            }
        });
        return;
    }
    
    UIView<IMYAlertShowViewProtocol> *payRetView = nil;
    // 有优惠倒计时，非支付成功，使用新样式挽回弹窗
    if (session.currentPricing.countdownTime > 0 && !session.isSuccess) {
        IMYSubGuidePayRetCancelledView *cancelView = [[IMYSubGuidePayRetCancelledView alloc] initWithSession:session];
        
        cancelView.onDismissedBlock = ^(NSInteger actionType) {
            // 点击继续支付
            if (actionType == 0) {
                [self payWithSession:session];
                return;
            }
            
            [[NSNotificationCenter defaultCenter] postNotificationName:KRightsSubGuideRaymentResultNotification
                                                                object:@{
                @"isSuccess": @(NO),
            }];
            
            if (session.onPayCompletedBlock) {
                session.onPayCompletedBlock(NO, NO);
            }
        };
        
        payRetView = cancelView;
        
    } else { // 旧挽回弹窗(成功弹窗)
        IMYSubGuidePayRetView *retView = [[IMYSubGuidePayRetView alloc] initWithSession:session];
        
        // 点击关闭
        [retView setCloseBlock:^(BOOL isSuccess) {
            [[NSNotificationCenter defaultCenter] postNotificationName:KRightsSubGuideRaymentResultNotification
                                                                object:@{
                @"isSuccess": @(isSuccess),
            }];
            
            if (session.onPayCompletedBlock) {
                session.onPayCompletedBlock(isSuccess, NO);
            }
        }];
        
        // 点击开始使用或继续支付
        [retView setConfirmBlock:^(BOOL isSuccess) {
            // 支付失败，点击继续支付
            if (!isSuccess) {
                [self payWithSession:session];
                return;
            }
            
            [[NSNotificationCenter defaultCenter] postNotificationName:KRightsSubGuideRaymentResultNotification
                                                                object:@{
                @"isSuccess": @(isSuccess),
            }];
            
            if (session.onPayCompletedBlock) {
                session.onPayCompletedBlock(isSuccess, NO);
            }
        }];
        
        payRetView = retView;
    }
    
    // 优先执行外部注入的Block
    if (onCancelBlock) {
        onCancelBlock(payRetView);
    } else {
        [payRetView show];
    }
}

- (void)showPayRetWithPopInfo:(NSDictionary *)popInfo
                      session:(IMYSubGuideSession *)session {
    if (IMYRightsSDK.isSubAuditReview) {
        // 审核状态下无需弹窗
        [UIWindow imy_showTextHUD:@"购买成功，欢迎您成为会员！"];
        imy_asyncMainBlock(^{
            [[NSNotificationCenter defaultCenter] postNotificationName:KRightsSubGuideRaymentResultNotification
                                                                object:@{
                @"isSuccess": @(YES),
            }];
            
            if (session.onPayCompletedBlock) {
                session.onPayCompletedBlock(YES, YES);
            }
        });
        return;
    }
    
    // 支付成功结果样式
    NSInteger retStyleV = 1;
    
    // 经期健康度场景
    if ([IMYSubGuideUnlockView inAbnormalABTestWithSceneKey:session.sceneKey]) {
        retStyleV = 2;
    }
    
    // 黄金备孕期场景
    if ([IMYSubGuideUnlockView inGoldenBeiyunABTestWithSceneKey:session.sceneKey]) {
        // 黄金备孕期限定 实验1 才弹新结果页
        IMYABTestExperiment *exp = [[IMYABTestManager sharedInstance] experimentForKey:@"shouyunqi"];
        if ([exp.vars integerForKey:@"beiyun"] == 1) {
            retStyleV = 2;
        }
    }
    
    // 产检智能解读
    if ([IMYSubGuideUnlockView inChanJianABTestWithSceneKey:session.sceneKey]) {
        retStyleV = 2;
    }
    
    IMYSubGuidePayRetSuccessView *payRetView = [IMYSubGuidePayRetSuccessView newWithRawData:popInfo styleV:retStyleV];
    payRetView.session = session;
    
    payRetView.onClosedBlock = ^{
        [[NSNotificationCenter defaultCenter] postNotificationName:KRightsSubGuideRaymentResultNotification
                                                            object:@{
            @"isSuccess": @(YES),
        }];
        
        if (session.onPayCompletedBlock) {
            session.onPayCompletedBlock(YES, YES);
        }
    };
    
    [payRetView show];
}

/// 是否来自会员中心 tab
+ (BOOL)isFromVIPCenterTab {
    __block BOOL ret = NO;
    UIViewController *vc = [UIViewController imy_currentViewControlloer];
    [vc.navigationController.viewControllers enumerateObjectsUsingBlock:^(__kindof UIViewController * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj isKindOfClass:[IMYPublicBaseViewController class]]) {
            IMYPublicBaseViewController *baseVC = (IMYPublicBaseViewController *)obj;
            ret = [baseVC.ga_appendParams[@"is_vip_center_page"] boolValue];
            if (ret) {
                *stop = YES;
            }
        }
    }];
    return ret;
}

/// sceneKey 映射成 publicType
+ (NSString *)biPublicTypeFromScnekey:(NSString *)sceneKey {
    NSString *publicType = sceneKey;
    
    if ([sceneKey isEqualToString:KRightsSceneKey_widget]) {
        publicType = @"桌面小组件";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_tab_widget]) {
        publicType = @"会员tab-桌面小组件";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_ai_analysis]) {
        publicType = @"智能分析";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_center]) {
        publicType = @"会员tab";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_tab_ai_analysis]) {
        publicType = @"会员tab-智能分析";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_kuaisuwenzhen]) {
        publicType = @"快速问诊";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_baogaojiedu]) {
        publicType = @"报告解读";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_beiyunzixun]) {
        publicType = @"报告解读";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_zhuanyepingce]) {
        publicType = @"专业测评";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_huafeichongzhi]) {
        publicType = @"话费充值页";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_waimaihongbao]) {
        publicType = @"外卖红包页";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_gouwufanxian]) {
        publicType = @"购物返现页";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_gengduoquanyi]) {
        publicType = @"更多权益页";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_my_tab]) {
        publicType = @"我tab";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_details]) {
        publicType = @"权益详情页";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_wallet]) {
        publicType = @"钱包页";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_ad]) {
        publicType = @"开屏广告";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_widget_card]) {
        publicType = @"桌面小组件-付费小组件";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_fanxianhongbao]) {
        publicType = @"返现红包";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_simple_mode]) {
        publicType = @"极简模式";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_center_top]) {
        publicType = @"会员中心-头部卡片按钮";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_center_bottom]) {
        publicType = @"会员中心-底部按钮";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_wanggoushangcheng]) {
        publicType = @"网购商城页";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_fuwushangcheng]) {
        publicType = @"服务商城页";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_shipinhuiyuanchongzhi]) {
        publicType = @"视频会员充值页";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_yima_time_recordtab]) {
        publicType = @"大姨妈具体时间记录-记录tab";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_yima_time_analysis]) {
        publicType = @"大姨妈具体时间记录-分析列表页";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_yima_time_home]) {
        publicType = @"大姨妈具体时间记录-首页";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_beiyun_golden]) {
        publicType = @"黄金受孕期";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_tab_beiyun_golden]) {
        publicType = @"会员tab-黄金受孕期";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_shengqianshangxiangye]) {
        publicType = @"省钱商详页";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_zhuanyepingce]) {
        publicType = @"会员tab-专业测评";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_simple_mode]) {
        publicType = @"会员tab-极简模式";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_bbj_long_video]) {
        publicType = @"宝宝记-长视频";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_meituanlianmingquan]) {
        publicType = @"美团联名券";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_lama_feeding_record]) {
        publicType = @"喂养记录智能分析";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_byunyu_special_course]) {
        publicType = @"专家课程";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_special_course]) {
        publicType = @"会员tab-专家课程";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_feeding_record]) {
        publicType = @"会员tab-喂养记录智能分析";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_quick_ask]) {
        publicType = @"互医-快速问";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_direct_ask]) {
        publicType = @"互医-定向问";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_vip_shengqian]) {
        publicType = @"会员tab-省钱权益";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_ai_chanjian_time]) {
        publicType = @"产检智能解读-产检时间表";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_ai_chanjian_tool]) {
        publicType = @"产检智能解读-小工具";
    } else if ([sceneKey isEqualToString:KRightsSceneKey_ai_chanjian_vip]) {
        publicType = @"产检智能解读-会员tab";
    }
    
    return publicType;
}

#pragma mark - x 天 x 次限制

/// x 天 x 次规则（只是获取不会更新本地值）
+ (IMYSubGuideLimitType)isLimitWithKey:(NSString *)key limitCount:(NSInteger)limitCount limitDays:(NSInteger)limitDays {
    IMYSubGuideLimitType limitType = IMYSubGuideLimitType_None;
    
    if (limitCount < 0 || limitDays <= 0) {
        NSLog(@"[x 天 x 次规则] 2 参数非法（duration <= 0、limit < 0）（不弹）");
        NSLog(@"[x 天 x 次规则] key = %@, limitCount = %ld, limitDays = %ld", key, limitCount, limitDays);
        return limitType;
    }
    
    if (limitCount == 0) {
        NSLog(@"[x 天 x 次规则] 3 limit == 0 （无限弹） ");
        limitType = IMYSubGuideLimitType_pass;
        return limitType;
    }
    
    // lastCount、days
    NSInteger lastCount = [self getLastCountForKey:key];
    NSTimeInterval lastTime = [self getLastTimeForKey:key];
    
    NSTimeInterval now = IMYDateTimeIntervalSince1970();
    NSInteger days = 0;
    if (lastTime > 0 && now > lastTime) {
        days = (now - lastTime) / (24 * 60 * 60);
    }
    
    // 开始判断
    if (lastCount == 0 || lastTime == 0) {
        limitType = IMYSubGuideLimitType_pass;
        NSLog(@"[x 天 x 次规则] 4 天数判断。通过-首次");
    } else if (days >= limitDays) {
        limitType = IMYSubGuideLimitType_pass_reset;
        NSLog(@"[x 天 x 次规则] 4 天数判断（超过天数限制，重置次数）");
    } else if (lastCount < limitCount) {
        limitType = IMYSubGuideLimitType_pass;
        NSLog(@"[x 天 x 次规则] 4 天数判断。通过");
    } else if (lastCount >= limitCount && days <= limitDays) {
        limitType = IMYSubGuideLimitType_limit;
        NSLog(@"[x 天 x 次规则] 4 天数判断。限制");
    }
    
    // debug 信息
    NSLog(@"[x 天 x 次规则] lastCount = %ld, lastTime = %f, days = %ld", lastCount, lastTime, days);
    NSLog(@"[x 天 x 次规则] key = %@, limitCount = %ld, limitDays = %ld", key, limitCount, limitDays);
    NSLog(@"[x 天 x 次规则] ===\n");
    
    return limitType;
}

/// x 天 x 次规则（+1）
+ (void)addLimitCountForKey:(NSString *)key {
    NSInteger lastCount = [self getLastCountForKey:key];
    [self setLastCount:lastCount + 1 forKey:key];
    [self setLastTime:IMYDateTimeIntervalSince1970() forKey:key];
}

/// x 天 x 次规则（重置为 1）
+ (void)resetLimitCountForKey:(NSString *)key {
    [self setLastCount:1 forKey:key];
    [self setLastTime:IMYDateTimeIntervalSince1970() forKey:key];
}

/// 清除 x 天 x 次规则
+ (void)clearLimitWithKey:(NSString *)key {
    [self setLastCount:0 forKey:key];
    [self setLastTime:0.0 forKey:key];
}

/// 清除 x 天 x 次规则
+ (void)clearLimitAll {
    IMYKV *kv = [IMYKV defaultKV];
    for (NSString *key in kv.allKeys) {
        if ([key hasPrefix:@"IMYSubGuide_limit"]) {
            [kv removeForKey:key];
        }
        if ([key hasPrefix:@"vip-popup-"]) {
            [kv removeForKey:key];
        }
    }
}

/// 忽略 x 天 x 次规则
+ (void)openLimitIgnore:(BOOL)isIgnore {
    [[IMYKV defaultKV] setBool:isIgnore forKey:@"#+IMYSubGuide_Limit_ignore"];
}

#pragma mark x 天 x 次限制 helper

+ (NSInteger )getLastCountForKey:(NSString *)key {
    NSString *lastCountKey = [NSString stringWithFormat:@"IMYSubGuide_limit_lastCountKey_%@", key];
    return [[IMYKV defaultKV] integerForKey:lastCountKey];
}

+ (void)setLastCount:(NSInteger)count forKey:(NSString *)key {
    NSString *lastCountKey = [NSString stringWithFormat:@"IMYSubGuide_limit_lastCountKey_%@", key];
    [[IMYKV defaultKV] setInteger:count forKey:lastCountKey];
}

+ (NSTimeInterval)getLastTimeForKey:(NSString *)key {
    NSString *lastTimeKey = [NSString stringWithFormat:@"IMYSubGuide_limit_lastTimeKey_%@", key];
    return [[IMYKV defaultKV] doubleForKey:lastTimeKey];
}

+ (void)setLastTime:(NSTimeInterval)lastTime forKey:(NSString *)key {
    NSString *lastTimeKey = [NSString stringWithFormat:@"IMYSubGuide_limit_lastTimeKey_%@", key];
    [[IMYKV defaultKV] setDouble:lastTime forKey:lastTimeKey];
}

#pragma mark x 天 x 次规则 - pricing

/// x 天 x 次规则
+ (IMYSubGuideLimitType)isLimitWithPricing:(IMYSubGuidePricingListItem *)pricing {
    if (!pricing.animation || !pricing) {
        NSLog(@"[x 天 x 次规则] 0 pricing（为空不弹）");
        NSLog(@"[x 天 x 次规则] 1 有开关优先开关（关闭不弹）");
        return IMYSubGuideLimitType_None;
    }
    
#ifdef DEBUG
    BOOL isIgnore = [[IMYKV defaultKV] boolForKey:@"#+IMYSubGuide_Limit_ignore"];
    if (isIgnore) {
        NSLog(@"[x 天 x 次规则] 通过（忽略规则）");
        return IMYSubGuideLimitType_Pass_ignore;
    }
#endif
    
    NSInteger limitCount = pricing.animation_limit;
    NSInteger limitDuration = pricing.animation_limit_duration;
    NSString *key = [NSString stringWithFormat:@"priceId%ld", pricing.id];
    return  [self isLimitWithKey:key limitCount:limitCount limitDays:limitDuration];
}

+ (void)addLimitCountWithPricing:(IMYSubGuidePricingListItem *)pricing {
    NSString *key = [NSString stringWithFormat:@"priceId%ld", pricing.id];
    [self addLimitCountForKey:key];
}

+ (void)resetLimitCountWithPricing:(IMYSubGuidePricingListItem *)pricing {
    NSString *key = [NSString stringWithFormat:@"priceId%ld", pricing.id];
    [self resetLimitCountForKey:key];
}

#pragma mark  x 天 x 次规则 - popup

/// x 天 x 次规则（只是获取不会更新本地值）
+ (IMYSubGuideLimitType)isLimitWithPopup:(IMYSubGuideBackPopupModel *)popup {
    if (!popup) {
        NSLog(@"[x 天 x 次规则] 0 back_popup（为空不弹）");
        return IMYSubGuideLimitType_None;
    }
    
#ifdef DEBUG
    BOOL isIgnore = [[IMYKV defaultKV] boolForKey:@"#+IMYSubGuide_Limit_ignore"];
    if (isIgnore) {
        NSLog(@"[x 天 x 次规则] -1 通过（忽略规则）");
        return IMYSubGuideLimitType_Pass_ignore;
    }
#endif
    
    NSInteger limitCount = popup.show_limit;
    NSInteger limitDuration = popup.show_limit_duration;
    return  [self isLimitWithKey:@"vipInfo_backup" limitCount:limitCount limitDays:limitDuration];
}

+ (void)addLimitCountWithPopup:(IMYSubGuideBackPopupModel *)popup {
    [self addLimitCountForKey:@"vipInfo_backup"];
}

+ (void)resetLimitCountWithPopup:(IMYSubGuideBackPopupModel *)popup {
    [self resetLimitCountForKey:@"vipInfo_backup"];
}

@end
