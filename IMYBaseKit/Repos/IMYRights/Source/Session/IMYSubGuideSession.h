//
//  IMYSubGuideSession.h
//  demo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/19.
//

#import <Foundation/Foundation.h>
#import "IMYSubGuideMacro.h"
#import "IMYSubGuideConfig.h"
#import "IMYSubGuideVipInfo.h"
#import "IMYAlertShowViewProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@class IMYSubGuidePayRetView;

@interface IMYSubGuideSession : NSObject

/// 标记成功要返回的 VC
@property (nonatomic, weak) UIViewController *fromVC;
@property (nonatomic, copy) NSString *sceneKey;
@property (nonatomic, assign) NSInteger planId;
@property (nonatomic, assign) NSInteger priceId;
/// 订阅类型：0：无订阅 1：订阅-月卡 2：订阅-季卡 3：连续包月 4：连续包季 5：连续包年 6：过期 7：订阅-年卡
@property (nonatomic, assign) NSInteger sub_type;
/// 赠礼id，可能多条
@property (nonatomic, copy) NSString *gift_info;
/// 赠礼促销id，可能多条
@property (nonatomic, copy) NSArray<NSNumber *> *gift_promotion_ids;
/// 价格包锚点
@property (nonatomic, assign) NSInteger sub_type_anchor;

/// 当前选中的价格包，需要它的倒计时
@property (nonatomic, strong) IMYSubGuidePricingListItem *currentPricing;

/// 优惠ID（购买时，优先使用该字段）
@property (nonatomic, assign) NSInteger user_promotion_id;

/// 执行预付费流程： http://wiki.meiyou.com/pages/viewpage.action?pageId=228038161
@property (nonatomic, assign) NSInteger quick_order;

/// 禁用部分流程弹窗 0：无禁用，1：禁用取消支付挽留弹窗，2：预留给其他弹窗
@property (nonatomic, assign) NSInteger disable_alerts;

/// 支付完成回调
@property (nonatomic, copy) void(^onPayCompletedBlock)(BOOL isSuccess, BOOL isNewStyle);

/// 传 config 则解锁组件不会请求网络
@property (nonatomic, strong) IMYSubGuideConfig *config;

#pragma mark - 权益详情页
@property (nonatomic, copy) NSString *currentRightGroupKey;
@property (nonatomic, copy) NSString *currentRightImageKey;


/// 用户取消拦截Block
@property (nonatomic, copy) void(^onPayUserCancelBlock)(UIView<IMYAlertShowViewProtocol> *payRetView);

#pragma mark - 支付结果
@property (nonatomic, assign) BOOL isSuccess; // 支付成功
@property (nonatomic, assign) BOOL isFromRestore; // 来自恢复订单

@end

NS_ASSUME_NONNULL_END
