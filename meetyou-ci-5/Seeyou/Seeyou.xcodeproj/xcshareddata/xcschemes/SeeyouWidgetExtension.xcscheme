<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1620"
   wasCreatedForAppExtension = "YES"
   version = "2.0">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES"
      buildArchitectures = "Automatic">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "8D3361022B624D1500FA162D"
               BuildableName = "SeeyouWidgetExtension.appex"
               BlueprintName = "SeeyouWidgetExtension"
               ReferencedContainer = "container:Seeyou.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "539D0A5816FC08030092F222"
               BuildableName = "Seeyou.app"
               BlueprintName = "Seeyou"
               ReferencedContainer = "container:Seeyou.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      shouldAutocreateTestPlan = "YES">
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = ""
      selectedLauncherIdentifier = "Xcode.IDEFoundation.Launcher.PosixSpawn"
      launchStyle = "0"
      askForAppToLaunch = "Yes"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES"
      launchAutomaticallySubstyle = "2">
      <RemoteRunnable
         runnableDebuggingMode = "2"
         BundleIdentifier = "com.apple.springboard">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "8D3361022B624D1500FA162D"
            BuildableName = "SeeyouWidgetExtension.appex"
            BlueprintName = "SeeyouWidgetExtension"
            ReferencedContainer = "container:Seeyou.xcodeproj">
         </BuildableReference>
      </RemoteRunnable>
      <EnvironmentVariables>
         <EnvironmentVariable
            key = "_XCWidgetKind"
            value = ""
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "_XCWidgetDefaultView"
            value = "timeline"
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "_XCWidgetFamily"
            value = "systemSmall"
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "_XCWidgetKind"
            value = ""
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "_XCWidgetKind"
            value = "weiyang_free_01"
            isEnabled = "YES">
         </EnvironmentVariable>
      </EnvironmentVariables>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES"
      askForAppToLaunch = "Yes"
      launchAutomaticallySubstyle = "2">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "539D0A5816FC08030092F222"
            BuildableName = "Seeyou.app"
            BlueprintName = "Seeyou"
            ReferencedContainer = "container:Seeyou.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
